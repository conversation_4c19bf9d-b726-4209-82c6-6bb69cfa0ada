{"name": "dark-theme-ide", "version": "1.0.0", "description": "A custom dark theme IDE built with React, Electron, and Python", "main": "electron/main.js", "homepage": "./", "scripts": {"start": "concurrently \"npm run start:python\" \"npm run start:react\" \"wait-on http://localhost:3000 && npm run start:electron\"", "start:react": "webpack serve --mode development", "start:electron": "cross-env NODE_ENV=development electron .", "start:python": "python3 python_backend/server.py", "build": "webpack --mode production", "build:electron": "electron-builder", "dist": "npm run build && npm run build:electron", "test": "echo \"Error: no test specified\" && exit 1"}, "build": {"appId": "com.darkthemeide.app", "productName": "Dark Theme IDE", "directories": {"output": "dist"}, "files": ["build/**/*", "electron/**/*", "python_backend/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.developer-tools"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "keywords": ["ide", "editor", "electron", "react", "python", "dark-theme"], "author": "", "license": "MIT", "dependencies": {"@monaco-editor/react": "^4.7.0", "@types/node": "^22.15.21", "@types/react": "^19.1.5", "@types/react-dom": "^19.1.5", "@types/styled-components": "^5.1.34", "css-loader": "^7.1.2", "electron": "^36.3.1", "html-webpack-plugin": "^5.6.3", "monaco-editor": "^0.52.2", "react": "^19.1.0", "react-dom": "^19.1.0", "style-loader": "^4.0.0", "styled-components": "^6.1.18", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.1"}, "devDependencies": {"concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron-builder": "^26.0.12", "wait-on": "^8.0.3"}}