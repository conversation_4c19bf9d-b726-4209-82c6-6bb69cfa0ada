/**
 * Sample Web Application JavaScript
 * Handles user interactions and API communication
 */

class UserManager {
    constructor() {
        this.users = [];
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadUsers();
    }

    bindEvents() {
        // Button event listeners
        document.getElementById('loadUsersBtn').addEventListener('click', () => this.loadUsers());
        document.getElementById('addUserBtn').addEventListener('click', () => this.showAddUserForm());
        document.getElementById('refreshBtn').addEventListener('click', () => this.refreshUsers());
        document.getElementById('cancelBtn').addEventListener('click', () => this.hideAddUserForm());
        
        // Form submission
        document.getElementById('userForm').addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    async loadUsers() {
        try {
            this.showLoading();
            const response = await fetch('/api/users');
            const result = await response.json();
            
            if (result.success) {
                this.users = result.data;
                this.renderUsers();
                this.showToast('Users loaded successfully', 'success');
            } else {
                throw new Error(result.error || 'Failed to load users');
            }
        } catch (error) {
            console.error('Error loading users:', error);
            this.showToast('Failed to load users: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async refreshUsers() {
        await this.loadUsers();
    }

    renderUsers() {
        const container = document.getElementById('userContainer');
        
        if (this.users.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <p>No users found. Add some users to get started!</p>
                </div>
            `;
            return;
        }

        container.innerHTML = this.users.map(user => `
            <div class="user-card" data-user-id="${user.id}">
                <div class="user-name">${this.escapeHtml(user.name)}</div>
                <div class="user-email">${this.escapeHtml(user.email)}</div>
                <div class="user-role role-${user.role}">${user.role}</div>
                <div class="user-actions">
                    <button class="btn btn-outline btn-sm" onclick="userManager.editUser(${user.id})">
                        Edit
                    </button>
                    <button class="btn btn-outline btn-sm" onclick="userManager.deleteUser(${user.id})">
                        Delete
                    </button>
                </div>
            </div>
        `).join('');
    }

    showAddUserForm() {
        document.getElementById('addUserForm').style.display = 'block';
        document.getElementById('userName').focus();
    }

    hideAddUserForm() {
        document.getElementById('addUserForm').style.display = 'none';
        this.clearForm();
    }

    clearForm() {
        document.getElementById('userForm').reset();
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const userData = {
            name: formData.get('name'),
            email: formData.get('email'),
            role: formData.get('role')
        };

        // Basic validation
        if (!userData.name || !userData.email) {
            this.showToast('Name and email are required', 'error');
            return;
        }

        if (!this.isValidEmail(userData.email)) {
            this.showToast('Please enter a valid email address', 'error');
            return;
        }

        try {
            this.showLoading();
            const response = await fetch('/api/users', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(userData)
            });

            const result = await response.json();

            if (result.success) {
                this.users.push(result.data);
                this.renderUsers();
                this.hideAddUserForm();
                this.showToast('User added successfully', 'success');
            } else {
                throw new Error(result.error || 'Failed to add user');
            }
        } catch (error) {
            console.error('Error adding user:', error);
            this.showToast('Failed to add user: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async editUser(userId) {
        const user = this.users.find(u => u.id === userId);
        if (!user) return;

        // Simple prompt-based editing (in a real app, you'd use a modal)
        const newName = prompt('Enter new name:', user.name);
        if (newName && newName !== user.name) {
            // In a real app, you'd make a PUT request to update the user
            user.name = newName;
            this.renderUsers();
            this.showToast('User updated successfully', 'success');
        }
    }

    async deleteUser(userId) {
        if (!confirm('Are you sure you want to delete this user?')) return;

        try {
            // In a real app, you'd make a DELETE request
            this.users = this.users.filter(u => u.id !== userId);
            this.renderUsers();
            this.showToast('User deleted successfully', 'success');
        } catch (error) {
            console.error('Error deleting user:', error);
            this.showToast('Failed to delete user', 'error');
        }
    }

    showLoading() {
        document.getElementById('loadingIndicator').style.display = 'flex';
    }

    hideLoading() {
        document.getElementById('loadingIndicator').style.display = 'none';
    }

    showToast(message, type = 'success') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Utility functions
const utils = {
    formatDate(dateString) {
        return new Date(dateString).toLocaleDateString();
    },

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    async fetchWithRetry(url, options = {}, retries = 3) {
        for (let i = 0; i < retries; i++) {
            try {
                const response = await fetch(url, options);
                if (response.ok) return response;
                throw new Error(`HTTP ${response.status}`);
            } catch (error) {
                if (i === retries - 1) throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }
};

// Initialize the application
let userManager;

document.addEventListener('DOMContentLoaded', () => {
    userManager = new UserManager();
    console.log('User Management System initialized');
});

// Export for testing
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UserManager, utils };
}
