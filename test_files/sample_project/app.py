#!/usr/bin/env python3
"""
Sample Flask Web Application
A simple web app demonstrating Python, HTML, CSS, and JavaScript integration
"""

from flask import Flask, render_template, jsonify, request
import json
import os
from datetime import datetime

app = Flask(__name__)

# Sample data
users = [
    {"id": 1, "name": "<PERSON>", "email": "<EMAIL>", "role": "admin"},
    {"id": 2, "name": "<PERSON>", "email": "<EMAIL>", "role": "user"},
    {"id": 3, "name": "<PERSON>", "email": "<EMAIL>", "role": "user"}
]

@app.route('/')
def index():
    """Main page route"""
    return render_template('index.html', users=users)

@app.route('/api/users')
def get_users():
    """API endpoint to get all users"""
    return jsonify({
        'success': True,
        'data': users,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/users/<int:user_id>')
def get_user(user_id):
    """API endpoint to get a specific user"""
    user = next((u for u in users if u['id'] == user_id), None)
    if user:
        return jsonify({
            'success': True,
            'data': user,
            'timestamp': datetime.now().isoformat()
        })
    else:
        return jsonify({
            'success': False,
            'error': 'User not found'
        }), 404

@app.route('/api/users', methods=['POST'])
def create_user():
    """API endpoint to create a new user"""
    data = request.get_json()
    
    if not data or 'name' not in data or 'email' not in data:
        return jsonify({
            'success': False,
            'error': 'Name and email are required'
        }), 400
    
    new_user = {
        'id': max(u['id'] for u in users) + 1,
        'name': data['name'],
        'email': data['email'],
        'role': data.get('role', 'user')
    }
    
    users.append(new_user)
    
    return jsonify({
        'success': True,
        'data': new_user,
        'timestamp': datetime.now().isoformat()
    }), 201

@app.route('/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

if __name__ == '__main__':
    # Development server configuration
    debug_mode = os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    port = int(os.getenv('PORT', 5000))
    
    print(f"Starting Flask application on port {port}")
    print(f"Debug mode: {debug_mode}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug_mode
    )
