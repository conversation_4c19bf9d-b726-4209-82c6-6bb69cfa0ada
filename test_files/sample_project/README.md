# Sample Web Application

A modern web application demonstrating full-stack development with Python, HTML, CSS, and JavaScript.

## Features

- **Flask Backend**: RESTful API with Python Flask
- **Modern Frontend**: Responsive HTML5, CSS3, and vanilla JavaScript
- **User Management**: CRUD operations for user data
- **Responsive Design**: Mobile-first approach with CSS Grid and Flexbox
- **Dark Mode Support**: Automatic dark mode based on system preferences
- **API Documentation**: RESTful endpoints with JSON responses

## Tech Stack

### Backend
- **Python 3.8+**
- **Flask 2.3+** - Web framework
- **Werkzeug** - WSGI utilities

### Frontend
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with custom properties
- **JavaScript ES6+** - Modern JavaScript features
- **Inter Font** - Typography

### Development Tools
- **ESLint** - JavaScript linting
- **Prettier** - Code formatting
- **Flake8** - Python linting
- **Black** - Python formatting

## Project Structure

```
sample_project/
├── app.py                 # Flask application
├── requirements.txt       # Python dependencies
├── package.json          # Node.js dependencies and scripts
├── README.md             # Project documentation
├── templates/            # HTML templates
│   └── index.html        # Main page template
└── static/               # Static assets
    ├── css/
    │   └── styles.css    # Application styles
    └── js/
        └── app.js        # Application JavaScript
```

## Installation

### Prerequisites
- Python 3.8 or higher
- Node.js 16 or higher (for development tools)

### Setup

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd sample_project
   ```

2. **Create a virtual environment:**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install Node.js dependencies (optional, for development tools):**
   ```bash
   npm install
   ```

## Usage

### Development Server

1. **Start the Flask development server:**
   ```bash
   python app.py
   ```
   Or using npm script:
   ```bash
   npm run dev
   ```

2. **Open your browser and navigate to:**
   ```
   http://localhost:5000
   ```

### Production Deployment

1. **Using Gunicorn:**
   ```bash
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

2. **Using Docker (create Dockerfile):**
   ```dockerfile
   FROM python:3.9-slim
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   COPY . .
   EXPOSE 5000
   CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:5000", "app:app"]
   ```

## API Endpoints

### Users API

- **GET /api/users** - Get all users
- **GET /api/users/{id}** - Get user by ID
- **POST /api/users** - Create new user
- **PUT /api/users/{id}** - Update user (not implemented)
- **DELETE /api/users/{id}** - Delete user (not implemented)

### Health Check

- **GET /health** - Application health status

### Example API Response

```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Alice Johnson",
      "email": "<EMAIL>",
      "role": "admin"
    }
  ],
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## Development

### Code Style

- **Python**: Follow PEP 8 guidelines, use Black for formatting
- **JavaScript**: Use ESLint configuration, Prettier for formatting
- **CSS**: Use BEM methodology for class naming

### Testing

```bash
# Run Python tests
python -m pytest tests/

# Run JavaScript linting
npm run lint

# Format code
npm run format
```

### Environment Variables

Create a `.env` file for environment-specific settings:

```env
FLASK_DEBUG=true
PORT=5000
SECRET_KEY=your-secret-key-here
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Flask documentation and community
- Modern CSS techniques and best practices
- JavaScript ES6+ features and patterns
