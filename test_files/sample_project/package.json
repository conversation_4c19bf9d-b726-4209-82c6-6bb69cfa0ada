{"name": "sample-web-app", "version": "1.0.0", "description": "A sample web application demonstrating Python, HTML, CSS, and JavaScript integration", "main": "app.py", "scripts": {"start": "python app.py", "dev": "FLASK_DEBUG=true python app.py", "test": "python -m pytest tests/", "lint": "eslint static/js/ && flake8 *.py", "format": "prettier --write static/js/ static/css/", "build": "echo 'Building application...'", "deploy": "echo 'Deploying application...'"}, "keywords": ["flask", "python", "web-app", "javascript", "html", "css", "sample"], "author": "Dark Theme IDE", "license": "MIT", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.8.0", "@eslint/js": "^8.0.0"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "repository": {"type": "git", "url": "https://github.com/example/sample-web-app.git"}, "bugs": {"url": "https://github.com/example/sample-web-app/issues"}, "homepage": "https://github.com/example/sample-web-app#readme"}