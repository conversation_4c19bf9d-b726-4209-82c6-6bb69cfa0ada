<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sample Web Application</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 class="title">User Management System</h1>
            <p class="subtitle">A sample web application built with Python, HTML, CSS, and JavaScript</p>
        </header>

        <main class="main-content">
            <section class="controls">
                <button id="loadUsersBtn" class="btn btn-primary">Load Users</button>
                <button id="addUserBtn" class="btn btn-secondary">Add New User</button>
                <button id="refreshBtn" class="btn btn-outline">Refresh</button>
            </section>

            <section class="user-list">
                <h2>Users</h2>
                <div id="userContainer" class="user-grid">
                    <!-- Users will be loaded here dynamically -->
                </div>
            </section>

            <section class="add-user-form" id="addUserForm" style="display: none;">
                <h3>Add New User</h3>
                <form id="userForm">
                    <div class="form-group">
                        <label for="userName">Name:</label>
                        <input type="text" id="userName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="userEmail">Email:</label>
                        <input type="email" id="userEmail" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="userRole">Role:</label>
                        <select id="userRole" name="role">
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Add User</button>
                        <button type="button" id="cancelBtn" class="btn btn-outline">Cancel</button>
                    </div>
                </form>
            </section>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Sample Web Application. Built with Flask, HTML, CSS, and JavaScript.</p>
        </footer>
    </div>

    <!-- Loading indicator -->
    <div id="loadingIndicator" class="loading-indicator" style="display: none;">
        <div class="spinner"></div>
        <p>Loading...</p>
    </div>

    <!-- Toast notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
