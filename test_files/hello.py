#!/usr/bin/env python3
"""
Test Python file for Dark Theme IDE
"""

def greet(name):
    """Greet someone with a friendly message."""
    return f"Hello, {name}! Welcome to Dark Theme IDE!"

def calculate_fibonacci(n):
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return calculate_fibonacci(n - 1) + calculate_fibonacci(n - 2)

def main():
    # Test the greeting function
    message = greet("Developer")
    print(message)
    
    # Calculate some Fibonacci numbers
    print("\nFibonacci sequence:")
    for i in range(10):
        fib = calculate_fibonacci(i)
        print(f"F({i}) = {fib}")
    
    # Test some basic Python features
    numbers = [1, 2, 3, 4, 5]
    squared = [x**2 for x in numbers]
    print(f"\nOriginal: {numbers}")
    print(f"Squared: {squared}")
    
    # Dictionary example
    person = {
        "name": "IDE User",
        "language": "Python",
        "editor": "Dark Theme IDE"
    }
    
    print(f"\nUser info: {person}")

if __name__ == "__main__":
    main()
