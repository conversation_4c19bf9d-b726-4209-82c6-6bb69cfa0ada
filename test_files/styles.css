/* CSS test file for Dark Theme IDE */
/* This demonstrates CSS syntax highlighting */

:root {
    --primary-color: #007acc;
    --secondary-color: #1e1e1e;
    --text-color: #d4d4d4;
    --border-color: #3e3e42;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--secondary-color);
    color: var(--text-color);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: linear-gradient(135deg, var(--primary-color), #0e639c);
    padding: 2rem;
    border-radius: 8px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.header p {
    text-align: center;
    opacity: 0.9;
}

.card {
    background-color: #252526;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s ease;
}

.button:hover {
    background-color: #0e639c;
}

.button:active {
    transform: translateY(1px);
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .card {
        padding: 1rem;
    }
}

/* Animation example */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}
