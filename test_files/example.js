// JavaScript test file for Dark Theme IDE
// This file demonstrates various JavaScript features

/**
 * A simple class to demonstrate object-oriented programming
 */
class Calculator {
    constructor() {
        this.history = [];
    }
    
    add(a, b) {
        const result = a + b;
        this.history.push(`${a} + ${b} = ${result}`);
        return result;
    }
    
    multiply(a, b) {
        const result = a * b;
        this.history.push(`${a} * ${b} = ${result}`);
        return result;
    }
    
    getHistory() {
        return this.history;
    }
}

// Arrow functions and modern JavaScript features
const greetUser = (name) => {
    return `Hello, ${name}! Welcome to Dark Theme IDE!`;
};

// Async/await example
async function fetchData() {
    try {
        // Simulated async operation
        await new Promise(resolve => setTimeout(resolve, 1000));
        return { message: "Data loaded successfully!" };
    } catch (error) {
        console.error("Error fetching data:", error);
    }
}

// Main function to demonstrate features
function main() {
    console.log(greetUser("JavaScript Developer"));
    
    // Test the calculator
    const calc = new Calculator();
    console.log("Calculator results:");
    console.log(calc.add(5, 3));
    console.log(calc.multiply(4, 7));
    console.log("History:", calc.getHistory());
    
    // Array methods
    const numbers = [1, 2, 3, 4, 5];
    const doubled = numbers.map(x => x * 2);
    const evens = numbers.filter(x => x % 2 === 0);
    
    console.log("Original numbers:", numbers);
    console.log("Doubled:", doubled);
    console.log("Even numbers:", evens);
    
    // Object destructuring
    const user = {
        name: "IDE User",
        language: "JavaScript",
        framework: "React"
    };
    
    const { name, language } = user;
    console.log(`User: ${name}, Language: ${language}`);
    
    // Test async function
    fetchData().then(data => {
        console.log(data);
    });
}

// Run the main function
main();
