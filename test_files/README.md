# Dark Theme IDE Test Files

This directory contains test files to demonstrate the capabilities of the Dark Theme IDE.

## Files Included

### 🐍 Python Files
- **hello.py** - Demonstrates Python syntax highlighting, functions, and basic programming concepts
  - Functions and docstrings
  - Fibonacci calculation
  - List comprehensions
  - Dictionary usage

### 🟨 JavaScript Files
- **example.js** - Shows modern JavaScript features
  - ES6 classes
  - Arrow functions
  - Async/await
  - Array methods
  - Object destructuring

### 🎨 CSS Files
- **styles.css** - CSS styling examples
  - CSS variables
  - Flexbox and Grid
  - Animations and transitions
  - Responsive design
  - Dark theme styling

## How to Test

1. **Open Files**: Use the file explorer on the left to navigate and open these files
2. **Syntax Highlighting**: Notice how each file type has appropriate syntax highlighting
3. **Edit Files**: Make changes to see the editor in action
4. **Run Python Code**: Copy code from hello.py and run it in the Python terminal
5. **Save Changes**: Use Ctrl+S to save modifications

## Features to Test

### ✅ File Operations
- [x] Open files from explorer
- [x] Create new files (Ctrl+N)
- [x] Save files (Ctrl+S)
- [x] Multiple tabs
- [x] File type detection

### ✅ Editor Features
- [x] Syntax highlighting
- [x] Line numbers
- [x] Code folding
- [x] Auto-indentation
- [x] Search and replace

### ✅ Terminal Features
- [x] Python code execution
- [x] Command line interface
- [x] Error handling
- [x] Output display

### ✅ UI Features
- [x] Dark theme
- [x] Resizable panels
- [x] Status bar information
- [x] Menu system

## Sample Python Code to Test

```python
# Copy this into the Python terminal
def test_ide():
    print("Testing Dark Theme IDE!")
    for i in range(5):
        print(f"Count: {i}")
    return "IDE test complete!"

result = test_ide()
print(result)
```

## Sample JavaScript Code

```javascript
// This shows JavaScript syntax highlighting
const testFunction = () => {
    console.log("JavaScript in Dark Theme IDE!");
    return [1, 2, 3].map(x => x * 2);
};

console.log(testFunction());
```

Enjoy testing the Dark Theme IDE! 🚀
