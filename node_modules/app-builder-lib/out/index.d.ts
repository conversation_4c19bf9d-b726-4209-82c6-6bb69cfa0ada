import { PublishOptions } from "electron-publish";
import { Packager } from "./packager";
import { PackagerOptions } from "./packagerApi";
export { Arch, archFromString, getArchSuffix } from "builder-util";
export { AppInfo } from "./appInfo";
export { AfterExtractContext, AfterPackContext, BeforePackContext, CommonConfiguration, Configuration, FuseOptionsV1, Hook, Hooks, MetadataDirectories, PackContext, } from "./configuration";
export { BeforeBuildContext, CompressionLevel, DEFAULT_TARGET, DIR_TARGET, Platform, SourceRepositoryInfo, Target, TargetConfigType, TargetConfiguration, TargetSpecificOptions, } from "./core";
export { ElectronBrandingOptions, ElectronDownloadOptions, ElectronPlatformName } from "./electron/ElectronFramework";
export { AppXOptions } from "./options/AppXOptions";
export { CommonWindowsInstallerConfiguration } from "./options/CommonWindowsInstallerConfiguration";
export { FileAssociation } from "./options/FileAssociation";
export { AppImageOptions, CommonLinuxOptions, DebOptions, FlatpakOptions, LinuxConfiguration, LinuxDesktopFile, LinuxTargetSpecificOptions } from "./options/linuxOptions";
export { DmgContent, DmgOptions, DmgWindow, MacConfiguration, MacOsTargetName, MasConfiguration } from "./options/macOptions";
export { AuthorMetadata, Metadata, RepositoryInfo } from "./options/metadata";
export { MsiOptions } from "./options/MsiOptions";
export { MsiWrappedOptions } from "./options/MsiWrappedOptions";
export { BackgroundAlignment, BackgroundScaling, PkgBackgroundOptions, PkgOptions } from "./options/pkgOptions";
export { AsarOptions, FileSet, FilesBuildOptions, PlatformSpecificBuildOptions, Protocol, ReleaseInfo } from "./options/PlatformSpecificBuildOptions";
export { PlugDescriptor, SlotDescriptor, SnapOptions } from "./options/SnapOptions";
export { SquirrelWindowsOptions } from "./options/SquirrelWindowsOptions";
export { WindowsAzureSigningConfiguration, WindowsConfiguration, WindowsSigntoolConfiguration } from "./options/winOptions";
export { BuildResult, Packager } from "./packager";
export { ArtifactBuildStarted, ArtifactCreated, PackagerOptions } from "./packagerApi";
export { CommonNsisOptions, CustomNsisBinary, NsisOptions, NsisWebOptions, PortableOptions } from "./targets/nsis/nsisOptions";
export { CancellationToken, ProgressInfo } from "builder-util-runtime";
export { PublishOptions, UploadTask } from "electron-publish";
export { WindowsSignOptions } from "./codeSign/windowsCodeSign";
export { CertificateFromStoreInfo, CustomWindowsSign, CustomWindowsSignTaskConfiguration, FileCodeSigningInfo, WindowsSignTaskConfiguration, } from "./codeSign/windowsSignToolManager";
export { ForgeOptions, buildForge } from "./forge-maker";
export { Framework, PrepareApplicationStageDirectoryOptions } from "./Framework";
export { LinuxPackager } from "./linuxPackager";
export { CustomMacSign, CustomMacSignOptions, MacPackager } from "./macPackager";
export { PlatformPackager } from "./platformPackager";
export { PublishManager } from "./publish/PublishManager";
export { WinPackager } from "./winPackager";
export declare function checkBuildRequestOptions(options: PackagerOptions & PublishOptions): void;
export declare function build(options: PackagerOptions & PublishOptions, packager?: Packager): Promise<Array<string>>;
