{"version": 3, "file": "windowsCodeSign.js", "sourceRoot": "", "sources": ["../../src/codeSign/windowsCodeSign.ts"], "names": [], "mappings": ";;AASA,kCAWC;AApBD,+CAAyC;AASlC,KAAK,UAAU,WAAW,CAAC,OAA2B,EAAE,QAAqB;IAClF,IAAI,OAAO,CAAC,OAAO,CAAC,gBAAgB,EAAE,CAAC;QACrC,IAAI,OAAO,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC;YACpC,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,mFAAmF,CAAC,CAAA;QACrG,CAAC;QACD,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,2CAA2C,CAAC,CAAA;IAC7F,CAAC;SAAM,CAAC;QACN,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,2BAA2B,CAAC,CAAA;IAC7E,CAAC;IACD,MAAM,cAAc,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAA;IAC1D,OAAO,aAAa,CAAC,KAAK,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAA;AACpE,CAAC;AAED,SAAS,aAAa,CAAC,MAA8B;IACnD,OAAO,IAAA,oBAAK,EAAC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAM,EAAE,EAAE;QAChD,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAA;QACzB;QACE,oEAAoE;QACpE,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC,4BAA4B,CAAC;;YAC/C,oEAAoE;YACpE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC,gCAAgC,CAAC,CAAA,EACnD,CAAC;YACD,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,EAAE,sDAAsD,CAAC,CAAA;YACpF,OAAO,IAAI,CAAA;QACb,CAAC;QACD,OAAO,KAAK,CAAA;IACd,CAAC,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import { log, retry } from \"builder-util\"\nimport { WindowsConfiguration } from \"../options/winOptions\"\nimport { WinPackager } from \"../winPackager\"\n\nexport interface WindowsSignOptions {\n  readonly path: string\n  readonly options: WindowsConfiguration\n}\n\nexport async function signWindows(options: WindowsSignOptions, packager: WinPackager): Promise<boolean> {\n  if (options.options.azureSignOptions) {\n    if (options.options.signtoolOptions) {\n      log.warn(null, \"ignoring signtool options, using Azure Trusted Signing; please only configure one\")\n    }\n    log.info({ path: log.filePath(options.path) }, \"signing with Azure Trusted Signing (beta)\")\n  } else {\n    log.info({ path: log.filePath(options.path) }, \"signing with signtool.exe\")\n  }\n  const packageManager = await packager.signingManager.value\n  return signWithRetry(async () => packageManager.signFile(options))\n}\n\nfunction signWithRetry(signer: () => Promise<boolean>): Promise<boolean> {\n  return retry(signer, 3, 1000, 1000, 0, (e: any) => {\n    const message = e.message\n    if (\n      // https://github.com/electron-userland/electron-builder/issues/1414\n      message?.includes(\"Couldn't resolve host name\") ||\n      // https://github.com/electron-userland/electron-builder/issues/8615\n      message?.includes(\"being used by another process.\")\n    ) {\n      log.warn({ error: message }, \"attempt to sign failed, another attempt will be made\")\n      return true\n    }\n    return false\n  })\n}\n"]}