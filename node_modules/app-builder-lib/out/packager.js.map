{"version": 3, "file": "packager.js", "sourceRoot": "", "sources": ["../src/packager.ts"], "names": [], "mappings": ";;;AAAA,+CAeqB;AACrB,+DAAwD;AACxD,uCAAoD;AACpD,8BAA6B;AAC7B,uCAA+B;AAC/B,2BAA4C;AAC5C,6BAA4B;AAC5B,uCAAmC;AACnC,sCAA0C;AAE1C,iCAA+D;AAC/D,oEAA6E;AAE7E,gEAA4D;AAI5D,uDAAmD;AACnD,2DAAgG;AAChG,iDAAmG;AACnG,wDAAkD;AAClD,oEAAwG;AACxG,4DAAuE;AACvE,0DAAyD;AACzD,4CAAgD;AAChD,sCAA8D;AAC9D,uCAA2C;AAC3C,gEAAyE;AAEzE,KAAK,UAAU,mBAAmB,CAAC,aAA4B,EAAE,QAAkB;IACjF,IAAI,SAAS,GAAG,aAAa,CAAC,SAAS,CAAA;IACvC,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QACtB,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAA;IACrC,CAAC;IAED,IAAI,WAAW,GAAG,aAAa,CAAC,WAAW,CAAA;IAC3C,IAAI,SAAS,KAAK,UAAU,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;QAClD,OAAO,MAAM,IAAA,kDAA8B,EAAC,aAAa,EAAE,QAAQ,CAAC,CAAA;IACtE,CAAC;IAED,IAAI,WAAW,IAAI,IAAI,IAAI,WAAW,KAAK,SAAS,EAAE,CAAC;QACrD,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAA;IACrC,CAAC;IAED,MAAM,aAAa,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;IAC/D,MAAM,aAAa,GAAG,aAAa,CAAC,eAAe,KAAK,KAAK,CAAA;IAC7D,IAAI,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,eAAe,EAAE,CAAC;QAC5D,OAAO,IAAI,iCAAe,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;IACvE,CAAC;SAAM,IAAI,SAAS,KAAK,OAAO,EAAE,CAAC;QACjC,OAAO,IAAI,+BAAc,CAAC,WAAW,EAAE,aAAa,EAAE,aAAa,CAAC,CAAA;IACtE,CAAC;SAAM,CAAC;QACN,MAAM,IAAI,wCAAyB,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAA;IACxE,CAAC;AACH,CAAC;AAmBD,MAAa,QAAQ;IAInB,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAGD,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAU,CAAA;IACxB,CAAC;IAID,IAAI,+BAA+B;QACjC,OAAO,IAAI,CAAC,6BAA6B,CAAA;IAC3C,CAAC;IAID,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAA;IACjC,CAAC;IAGD,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAID,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,cAAe,CAAA;IAC7B,CAAC;IAOD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAS,CAAA;IACvB,CAAC;IAUD,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAA;IACnC,CAAC;IAID,qBAAqB,CAAC,QAAyB,EAAE,UAAmB,IAAI;QACtE,IAAI,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAA;QACjC,IAAI,oBAAoB,GAAyB,IAAI,CAAA;QACrD,IAAI,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;YACvE,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;YACvE,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;gBACjC,GAAG,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAA;YAC5B,CAAC;QACH,CAAC;QAED,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAC7C,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,GAAG,IAAA,8CAAwB,EAAC,IAAI,CAAC,MAAM,EAAE,oBAAoB,EAAE,OAAO,CAAC,CAAA;YAC7E,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;QAC1C,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAQD,IAAI,iBAAiB;QACnB,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAA;QACpC,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,6BAA6B,CAAC,CAAA;YAC1E,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAA;QAClC,CAAC;QACD,OAAO,MAAM,CAAA;IACf,CAAC;IAED,IAAI,6BAA6B;QAC/B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,cAAe,CAAA;IACjD,CAAC;IAGD,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAW,CAAA;IACzB,CAAC;IAID,oBAAoB,CAAC,QAA6B;QAChD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/B,CAAC;IAED,oCAAoC;IACpC,YACE,OAAwB,EACf,oBAAoB,IAAI,wCAAiB,EAAE;QAA3C,sBAAiB,GAAjB,iBAAiB,CAA0B;QAtG9C,cAAS,GAAoB,IAAI,CAAA;QAKjC,kCAA6B,GAAG,KAAK,CAAA;QAMrC,wBAAmB,GAAG,KAAK,CAAA;QAM3B,iBAAY,GAAoB,IAAI,CAAA;QAKpC,mBAAc,GAAyB,IAAI,CAAA;QAMnD,sCAAiC,GAAG,KAAK,CAAA;QAExB,iBAAY,GAAG,IAAI,qCAAiB,EAAkB,CAAA;QAEvE,aAAQ,GAAmB,IAAI,CAAA;QAKtB,mBAAc,GAAG,IAAI,qBAAM,CAAC,UAAU,CAAC,CAAA;QAExC,oBAAe,GAAG,IAAI,eAAI,CAA8B,GAAG,EAAE,CAAC,IAAA,kCAAiB,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;QAIjI,gBAAW,GAAG,IAAI,0BAAW,CAAC,kBAAG,CAAC,cAAc,CAAC,CAAA;QAMlD,uBAAkB,GAAG,IAAI,GAAG,EAA4B,CAAA;QAoBhE,2BAAsB,GAA4E,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE;YAC3H,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,IAAA,kCAAmB,EAAC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC/F,CAAC,CAAA;QAEO,uBAAkB,GAAkB,IAAI,CAAA;QAexC,eAAU,GAAqB,IAAI,CAAA;QAK1B,cAAS,GAA+B,EAAE,CAAA;QAWzD,IAAI,aAAa,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,IAAI,wCAAyB,CAAC,qEAAqE,CAAC,CAAA;QAC5G,CAAC;QACD,IAAI,eAAe,IAAI,OAAO,EAAE,CAAC;YAC/B,MAAM,IAAI,wCAAyB,CAAC,qFAAqF,CAAC,CAAA;QAC5H,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,GAAG,EAAsC,CAAA;QAChF,IAAI,OAAO,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;YAC5B,OAAO,CAAC,OAAO,GAAG,OAAO,CAAA;QAC3B,CAAC;QAED,SAAS,cAAc,CAAC,QAAkB,EAAE,KAAoB;YAC9D,SAAS,UAAU,CAAC,qBAA8B;gBAChD,MAAM,MAAM,GAAG,KAAK,EAAQ,CAAA;gBAC5B,OAAO,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAA;YAC/F,CAAC;YAED,IAAI,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;YACtC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,UAAU,GAAG,IAAI,GAAG,EAAuB,CAAA;gBAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;YACnC,CAAC;YAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;oBACrC,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,CAAA;gBAC1B,CAAC;gBACD,OAAM;YACR,CAAC;YAED,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA;gBACvC,IAAI,SAAS,GAAG,CAAC,EAAE,CAAC;oBAClB,IAAA,uBAAQ,EAAC,UAAU,EAAE,IAAA,6BAAc,EAAC,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAA;gBACnG,CAAC;qBAAM,CAAC;oBACN,KAAK,MAAM,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;wBACpC,IAAA,uBAAQ,EAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;oBAClC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,cAAc,CAAC,eAAQ,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;QAC3C,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAC1B,cAAc,CAAC,eAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;QAC/C,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;YACxB,cAAc,CAAC,eAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,CAAA;QAC/C,CAAC;QAED,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;QAC/F,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,UAAU,CAAA;QAC9B,IAAI,CAAC,OAAO,GAAG;YACb,GAAG,OAAO;YACV,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,WAAW,CAAC;SACrG,CAAA;QAED,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAe,EAAE,EAAE,EAAE,IAAA,YAAY,GAAE,EAAE,EAAE,kBAAkB,CAAC,CAAA;IAChF,CAAC;IAED,KAAK,CAAC,wBAAwB;QAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAA;QAC7B,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,sBAAsB,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,EAAE,MAAM,CAAC,CAAA;QAC3I,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,wBAAwB,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,EAAE,wBAAwB,CAAC,EAAE,MAAM,CAAC,CAAA;QAEjJ,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,qBAAqB,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,EAAE,MAAM,CAAC,CAAA;QACxI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,mBAAmB,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,EAAE,MAAM,CAAC,CAAA;QAElI,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC,CAAA;QAC7G,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,cAAc,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAA;QACnH,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,CAAA;QAC1G,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,EAAE,MAAM,IAAA,yBAAe,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,MAAM,CAAC,CAAA;IAC5G,CAAC;IAED,WAAW,CAAC,OAAoC;QAC9C,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAA;IACb,CAAC;IAED,iBAAiB,CAAC,OAA0C;QAC1D,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAA;QAChD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,4BAA4B,CAAC,KAA2B,EAAE,IAA6B;QACrF,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;IACvD,CAAC;IAED,2BAA2B;QACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAA;IAC3B,CAAC;IAED,KAAK,CAAC,wBAAwB,CAAC,KAA2B,EAAE,SAAe;QACzE,kBAAG,CAAC,IAAI,CACN,SAAS,IAAI;YACX,MAAM,EAAE,KAAK,CAAC,qBAAqB;YACnC,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAClD,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC;SAC/B,EACD,UAAU,CACX,CAAA;QACD,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CAAC,KAAsB;QAC9C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAA;IACxD,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,KAAsB;QACrD,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAA;QAC7D,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAA;IACvC,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,IAAY;QACxC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAA;IAC3D,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,IAAY;QACtC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAA;IACzD,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA0B;QAC7C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;IACrD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAyB;QAC3C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAyB;QAC3C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;IACpD,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,OAAyB;QAC9C,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;IACvD,CAAC;IAED,KAAK,CAAC,cAAc;QAClB,IAAI,UAAU,GAAkB,IAAI,CAAA;QACpC,IAAI,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAA;QAC3C,IAAI,OAAO,iBAAiB,KAAK,QAAQ,EAAE,CAAC;YAC1C,8BAA8B;YAC9B,UAAU,GAAG,iBAAiB,CAAA;YAC9B,iBAAiB,GAAG,IAAI,CAAA;QAC1B,CAAC;aAAM,IAAI,iBAAiB,IAAI,IAAI,IAAI,OAAO,iBAAiB,CAAC,OAAO,KAAK,QAAQ,IAAI,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YACjI,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAA;YACtC,OAAO,iBAAiB,CAAC,OAAO,CAAA;QAClC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;QAElC,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;QAC5D,IAAI,CAAC,YAAY,GAAG,MAAM,IAAA,mCAAoB,EAAC,IAAA,iCAAe,EAAC,cAAc,CAAC,CAAC,CAAA;QAE/E,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAA;QACpC,MAAM,aAAa,GAAG,MAAM,IAAA,kBAAS,EAAC,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAC9H,IAAI,kBAAG,CAAC,cAAc,EAAE,CAAC;YACvB,kBAAG,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,sBAAsB,CAAC,aAAa,CAAC,EAAE,EAAE,kBAAkB,CAAC,CAAA;QAClF,CAAC;QAED,IAAI,CAAC,OAAO,GAAG,MAAM,IAAA,mCAA0B,EAAC,UAAU,EAAE,aAAa,CAAC,WAAY,CAAC,GAAG,CAAC,CAAA;QAC3F,IAAI,CAAC,iCAAiC,GAAG,IAAI,CAAC,OAAO,KAAK,UAAU,CAAA;QAEpE,MAAM,cAAc,GAAG,IAAI,CAAC,iCAAiC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,cAAc,CAAA;QAEvH,+CAA+C;QAC/C,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,CAAC;YACxE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,CAAA;QACnC,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,mDAAmD,CAAC,cAAc,CAAC,CAAA;QACjG,CAAC;QACD,IAAA,yBAAU,EAAC,IAAI,CAAC,QAAQ,EAAE,aAAa,CAAC,aAAa,CAAC,CAAA;QAEtD,IAAI,IAAI,CAAC,iCAAiC,EAAE,CAAC;YAC3C,kBAAG,CAAC,KAAK,CAAC,EAAE,cAAc,EAAE,cAAc,EAAE,EAAE,oCAAoC,CAAC,CAAA;QACrF,CAAC;QACD,IAAA,+BAAa,EAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,EAAE,cAAc,EAAE,cAAc,CAAC,CAAA;QAE9E,MAAM,IAAA,8BAAqB,EAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAE5D,IAAI,CAAC,cAAc,GAAG,aAAa,CAAA;QACnC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAA;IACjC,CAAC;IAED,oJAAoJ;IACpJ,KAAK,CAAC,KAAK,CAAC,cAAqC;QAC/C,MAAM,IAAI,CAAC,cAAc,EAAE,CAAA;QAE3B,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;YAC3B,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAA;QAC9D,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;QACvC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAA;QAErC,IAAI,CAAC,UAAU,GAAG,MAAM,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAE9D,MAAM,kCAAkC,GAAG,IAAI,CAAC,OAAO,CACrD,IAAI,CAAC,UAAU,EACf,IAAA,2BAAW,EAAC,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,MAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE;YACjE,EAAE,EAAE,EAAE;SACP,CAAC,CACH,CAAA;QAED,IAAI,CAAC,IAAI,IAAK,OAAO,CAAC,MAAc,CAAC,KAAK,EAAE,CAAC;YAC3C,MAAM,mBAAmB,GAAG,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,+BAA+B,CAAC,CAAA;YAC1G,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAG,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE,EAAE,0BAA0B,CAAC,CAAA;YACjF,MAAM,IAAA,qBAAU,EAAC,mBAAmB,EAAE,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QAC5E,CAAC;QAED,wFAAwF;QACxF,MAAM,aAAa,GAAG,IAAI,GAAG,EAAU,CAAA;QACvC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAC7B,IAAI,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;gBACvB,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;YAC/B,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,CAAA;QAC9D,MAAM,iBAAiB,GAAG,MAAM,IAAA,6BAAc,EAAC,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,IAAI,EAAE;YACxE,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC/B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,kCAAkC,EAAE,mBAAmB,CAAC,CAAC,CAAA;YACjG,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAA;YACxC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAA;YACzB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,MAAM,QAAQ,EAAE,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,EAAE;oBAChC,kBAAG,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,gBAAgB,CAAC,CAAA;gBAC1C,CAAC,CAAC,CAAA;YACJ,CAAC;QACH,CAAC,CAAC,CAAA;QAEF,OAAO;YACL,MAAM,EAAE,kCAAkC;YAC1C,aAAa,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC;YACxC,iBAAiB;YACjB,aAAa,EAAE,IAAI,CAAC,MAAM;SAC3B,CAAA;IACH,CAAC;IAEO,KAAK,CAAC,mDAAmD,CAAC,cAAsB;QACtF,IAAI,IAAI,GAAG,MAAM,IAAA,mCAAoB,EAAC,IAAA,iCAAe,EAAC,cAAc,CAAC,CAAC,CAAA;QACtE,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAO,IAAI,CAAA;QACb,CAAC;QAED,IAAI,GAAG,MAAM,IAAA,mCAAoB,EAAC,IAAA,mBAAY,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE,cAAc,CAAC,CAAC,CAAA;QACvG,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAA;YAC/B,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC,CAAA;IACpF,CAAC;IAEO,KAAK,CAAC,OAAO;QACnB,MAAM,WAAW,GAAG,IAAI,+BAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAA;QAChE,MAAM,gBAAgB,GAAG,EAAc,CAAA;QAEvC,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAAiC,CAAA;QACjE,MAAM,cAAc,GAAG,IAAI,GAAG,EAAU,CAAA;QAExC,KAAK,MAAM,CAAC,QAAQ,EAAE,UAAU,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,OAAQ,EAAE,CAAC;YAC3D,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBACrC,MAAK;YACP,CAAC;YAED,IAAI,QAAQ,KAAK,eAAQ,CAAC,GAAG,IAAI,OAAO,CAAC,QAAQ,KAAK,eAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;gBAChF,MAAM,IAAI,wCAAyB,CAAC,oGAAoG,CAAC,CAAA;YAC3I,CAAC;YAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAClD,MAAM,YAAY,GAAwB,IAAI,GAAG,EAAE,CAAA;YACnD,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;YAE5C,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,IAAA,2CAA2B,EAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC9F,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;oBACrC,MAAK;gBACP,CAAC;gBAED,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,WAAY,CAAC,MAAO,EAAE,mBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;gBAChH,MAAM,UAAU,GAAG,IAAA,6BAAa,EAAC,YAAY,EAAE,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAA;gBACjI,MAAM,kBAAkB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;gBACpD,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;YAC5D,CAAC;YAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,CAAC;gBACrC,MAAK;YACP,CAAC;YAED,KAAK,MAAM,MAAM,IAAI,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;gBAC3C,IAAI,MAAM,CAAC,gBAAgB,EAAE,CAAC;oBAC5B,WAAW,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAA;gBAC3C,CAAC;qBAAM,CAAC;oBACN,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAC/B,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,WAAW,CAAC,UAAU,EAAE,CAAA;QAE9B,KAAK,MAAM,MAAM,IAAI,gBAAgB,EAAE,CAAC;YACtC,MAAM,MAAM,CAAC,WAAW,EAAE,CAAA;QAC5B,CAAC;QACD,OAAO,gBAAgB,CAAA;IACzB,CAAC;IAEO,KAAK,CAAC,YAAY,CAAC,QAAkB;QAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,uBAAuB,IAAI,IAAI,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;QAC7D,CAAC;QAED,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,eAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClB,MAAM,WAAW,GAAG,CAAC,2CAAa,eAAe,EAAC,CAAC,CAAC,WAAW,CAAA;gBAC/D,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;YAED,KAAK,eAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;gBACtB,MAAM,WAAW,GAAG,CAAC,2CAAa,eAAe,EAAC,CAAC,CAAC,WAAW,CAAA;gBAC/D,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;YAED,KAAK,eAAQ,CAAC,KAAK;gBACjB,OAAO,IAAI,CAAC,2CAAa,iBAAiB,EAAC,CAAC,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;YAElE;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,EAAE,CAAC,CAAA;QACpD,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,sBAAsB,CAAC,QAAkB,EAAE,IAAU;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,oBAAoB,EAAE,CAAC;YAC7E,OAAM;QACR,CAAC;QAED,MAAM,aAAa,GAAG,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,EAAE,IAAI,EAAE,CAAA;QAC9E,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,IAAI,MAAM,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;YACnC,MAAM,IAAA,qBAAc,EAAC,QAAQ,CAAC,QAAQ,EAAE,mBAAI,CAAC,IAAI,CAAC,EAAE,aAAa,CAAC,CAAA;QACpE,CAAC;QAED,IAAI,MAAM,CAAC,UAAU,KAAK,KAAK,EAAE,CAAC;YAChC,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,4BAA4B,EAAE,EAAE,8BAA8B,CAAC,CAAA;YAClF,OAAM;QACR,CAAC;QAED,MAAM,WAAW,GAAG,MAAM,IAAA,yBAAe,EAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE,aAAa,CAAC,CAAA;QAC/F,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,mCAAmC,GAAG,MAAM,WAAW,CAAC;gBAC5D,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,eAAe,EAAE,IAAI,CAAC,MAAM,CAAC,eAAgB;gBAC7C,QAAQ;gBACR,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;aACjB,CAAC,CAAA;YAEF,6GAA6G;YAC7G,IAAI,CAAC,6BAA6B,GAAG,CAAC,mCAAmC,CAAA;YACzE,IAAI,CAAC,mCAAmC,EAAE,CAAC;gBACzC,OAAM;YACR,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,2BAA2B,KAAK,IAAI,IAAI,QAAQ,CAAC,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC1F,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,sEAAsE,EAAE,EAAE,8BAA8B,CAAC,CAAA;QAC9H,CAAC;aAAM,CAAC;YACN,MAAM,IAAA,uBAAgB,EAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;gBAC1C,aAAa;gBACb,QAAQ,EAAE,QAAQ,CAAC,QAAQ;gBAC3B,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC;gBAChB,cAAc,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,EAAE,KAAK,CAAmC;aAC1F,CAAC,CAAA;QACJ,CAAC;IACH,CAAC;CACF;AA9eD,4BA8eC;AAED,SAAS,kBAAkB,CAAC,UAAyB,EAAE,cAA2B;IAChF,MAAM,OAAO,GAAG,IAAI,GAAG,EAAU,CAAA;IACjC,KAAK,MAAM,MAAM,IAAI,UAAU,EAAE,CAAC;QAChC,yCAAyC;QACzC,IAAI,MAAM,YAAY,0BAAU,EAAE,CAAC;YACjC,SAAQ;QACV,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAA;QAC5B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;QACrB,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QACvB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAChB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;SAChB,IAAI,EAAE;SACN,GAAG,CAAC,GAAG,CAAC,EAAE;QACT,OAAO,IAAA,iBAAM,EAAC,GAAG,CAAC;aACf,IAAI,CAAC,GAAG,EAAE,CAAC,IAAA,gBAAK,EAAC,GAAG,EAAE,KAAK,CAAC,CAAC,oBAAoB,CAAC;aAClD,IAAI,CAAC,GAAG,EAAE,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAA;IACxC,CAAC,CAAC,CACL,CAAA;AACH,CAAC;AASD,SAAS,sBAAsB,CAAC,aAA4B;IAC1D,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAA,gCAAiB,EAAC,aAAa,CAAC,CAAC,CAAA;IACtD,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC;QACtB,CAAC,CAAC,OAAO,GAAG,qBAAqB,CAAA;IACnC,CAAC;IACD,OAAO,IAAA,8BAAe,EAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AACjC,CAAC", "sourcesContent": ["import {\n  addValue,\n  Arch,\n  archFromString,\n  AsyncTaskManager,\n  DebugLogger,\n  deepAssign,\n  executeFinally,\n  getArtifactArchName,\n  InvalidConfigurationError,\n  log,\n  orNullIfFileNotExist,\n  safeStringifyJson,\n  serializeToYaml,\n  TmpDir,\n} from \"builder-util\"\nimport { CancellationToken } from \"builder-util-runtime\"\nimport { chmod, mkdirs, outputFile } from \"fs-extra\"\nimport * as isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport { release as getOsRelease } from \"os\"\nimport * as path from \"path\"\nimport { AppInfo } from \"./appInfo\"\nimport { readAsarJson } from \"./asar/asar\"\nimport { AfterExtractContext, AfterPackContext, BeforePackContext, Configuration, Hook } from \"./configuration\"\nimport { Platform, SourceRepositoryInfo, Target } from \"./core\"\nimport { createElectronFrameworkSupport } from \"./electron/ElectronFramework\"\nimport { Framework } from \"./Framework\"\nimport { LibUiFramework } from \"./frameworks/LibUiFramework\"\nimport { Metadata } from \"./options/metadata\"\nimport { ArtifactBuildStarted, ArtifactCreated, PackagerOptions } from \"./packagerApi\"\nimport { PlatformPackager } from \"./platformPackager\"\nimport { ProtonFramework } from \"./ProtonFramework\"\nimport { computeArchToTargetNamesMap, createTargets, NoOpTarget } from \"./targets/targetFactory\"\nimport { computeDefaultAppDirectory, getConfig, validateConfiguration } from \"./util/config/config\"\nimport { expandMacro } from \"./util/macroExpander\"\nimport { createLazyProductionDeps, NodeModuleDirInfo, NodeModuleInfo } from \"./util/packageDependencies\"\nimport { checkMetadata, readPackageJson } from \"./util/packageMetadata\"\nimport { getRepositoryInfo } from \"./util/repositoryInfo\"\nimport { resolveFunction } from \"./util/resolve\"\nimport { installOrRebuild, nodeGypRebuild } from \"./util/yarn\"\nimport { PACKAGE_VERSION } from \"./version\"\nimport { AsyncEventEmitter, HandlerType } from \"./util/asyncEventEmitter\"\n\nasync function createFrameworkInfo(configuration: Configuration, packager: Packager): Promise<Framework> {\n  let framework = configuration.framework\n  if (framework != null) {\n    framework = framework.toLowerCase()\n  }\n\n  let nodeVersion = configuration.nodeVersion\n  if (framework === \"electron\" || framework == null) {\n    return await createElectronFrameworkSupport(configuration, packager)\n  }\n\n  if (nodeVersion == null || nodeVersion === \"current\") {\n    nodeVersion = process.versions.node\n  }\n\n  const distMacOsName = `${packager.appInfo.productFilename}.app`\n  const isUseLaunchUi = configuration.launchUiVersion !== false\n  if (framework === \"proton\" || framework === \"proton-native\") {\n    return new ProtonFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  } else if (framework === \"libui\") {\n    return new LibUiFramework(nodeVersion, distMacOsName, isUseLaunchUi)\n  } else {\n    throw new InvalidConfigurationError(`Unknown framework: ${framework}`)\n  }\n}\n\ntype PackagerEvents = {\n  artifactBuildStarted: Hook<ArtifactBuildStarted, void>\n\n  beforePack: Hook<BeforePackContext, void>\n  afterExtract: Hook<AfterExtractContext, void>\n  afterPack: Hook<AfterPackContext, void>\n  afterSign: Hook<AfterPackContext, void>\n\n  artifactBuildCompleted: Hook<ArtifactCreated, void>\n\n  msiProjectCreated: Hook<string, void>\n  appxManifestCreated: Hook<string, void>\n\n  // internal-use only, prefer usage of `artifactBuildCompleted`\n  artifactCreated: Hook<ArtifactCreated, void>\n}\n\nexport class Packager {\n  readonly projectDir: string\n\n  private _appDir: string\n  get appDir(): string {\n    return this._appDir\n  }\n\n  private _metadata: Metadata | null = null\n  get metadata(): Metadata {\n    return this._metadata!\n  }\n\n  private _nodeModulesHandledExternally = false\n\n  get areNodeModulesHandledExternally(): boolean {\n    return this._nodeModulesHandledExternally\n  }\n\n  private _isPrepackedAppAsar = false\n\n  get isPrepackedAppAsar(): boolean {\n    return this._isPrepackedAppAsar\n  }\n\n  private _devMetadata: Metadata | null = null\n  get devMetadata(): Metadata | null {\n    return this._devMetadata\n  }\n\n  private _configuration: Configuration | null = null\n\n  get config(): Configuration {\n    return this._configuration!\n  }\n\n  isTwoPackageJsonProjectLayoutUsed = false\n\n  private readonly eventEmitter = new AsyncEventEmitter<PackagerEvents>()\n\n  _appInfo: AppInfo | null = null\n  get appInfo(): AppInfo {\n    return this._appInfo!\n  }\n\n  readonly tempDirManager = new TmpDir(\"packager\")\n\n  private _repositoryInfo = new Lazy<SourceRepositoryInfo | null>(() => getRepositoryInfo(this.projectDir, this.metadata, this.devMetadata))\n\n  readonly options: PackagerOptions\n\n  readonly debugLogger = new DebugLogger(log.isDebugEnabled)\n\n  get repositoryInfo(): Promise<SourceRepositoryInfo | null> {\n    return this._repositoryInfo.value\n  }\n\n  private nodeDependencyInfo = new Map<string, Lazy<Array<any>>>()\n\n  getNodeDependencyInfo(platform: Platform | null, flatten: boolean = true): Lazy<Array<NodeModuleInfo | NodeModuleDirInfo>> {\n    let key = \"\" + flatten.toString()\n    let excludedDependencies: Array<string> | null = null\n    if (platform != null && this.framework.getExcludedDependencies != null) {\n      excludedDependencies = this.framework.getExcludedDependencies(platform)\n      if (excludedDependencies != null) {\n        key += `-${platform.name}`\n      }\n    }\n\n    let result = this.nodeDependencyInfo.get(key)\n    if (result == null) {\n      result = createLazyProductionDeps(this.appDir, excludedDependencies, flatten)\n      this.nodeDependencyInfo.set(key, result)\n    }\n    return result\n  }\n\n  stageDirPathCustomizer: (target: Target, packager: PlatformPackager<any>, arch: Arch) => string = (target, packager, arch) => {\n    return path.join(target.outDir, `__${target.name}-${getArtifactArchName(arch, target.name)}`)\n  }\n\n  private _buildResourcesDir: string | null = null\n\n  get buildResourcesDir(): string {\n    let result = this._buildResourcesDir\n    if (result == null) {\n      result = path.resolve(this.projectDir, this.relativeBuildResourcesDirname)\n      this._buildResourcesDir = result\n    }\n    return result\n  }\n\n  get relativeBuildResourcesDirname(): string {\n    return this.config.directories!.buildResources!\n  }\n\n  private _framework: Framework | null = null\n  get framework(): Framework {\n    return this._framework!\n  }\n\n  private readonly toDispose: Array<() => Promise<void>> = []\n\n  disposeOnBuildFinish(disposer: () => Promise<void>) {\n    this.toDispose.push(disposer)\n  }\n\n  //noinspection JSUnusedGlobalSymbols\n  constructor(\n    options: PackagerOptions,\n    readonly cancellationToken = new CancellationToken()\n  ) {\n    if (\"devMetadata\" in options) {\n      throw new InvalidConfigurationError(\"devMetadata in the options is deprecated, please use config instead\")\n    }\n    if (\"extraMetadata\" in options) {\n      throw new InvalidConfigurationError(\"extraMetadata in the options is deprecated, please use config.extraMetadata instead\")\n    }\n\n    const targets = options.targets || new Map<Platform, Map<Arch, Array<string>>>()\n    if (options.targets == null) {\n      options.targets = targets\n    }\n\n    function processTargets(platform: Platform, types: Array<string>) {\n      function commonArch(currentIfNotSpecified: boolean): Array<Arch> {\n        const result = Array<Arch>()\n        return result.length === 0 && currentIfNotSpecified ? [archFromString(process.arch)] : result\n      }\n\n      let archToType = targets.get(platform)\n      if (archToType == null) {\n        archToType = new Map<Arch, Array<string>>()\n        targets.set(platform, archToType)\n      }\n\n      if (types.length === 0) {\n        for (const arch of commonArch(false)) {\n          archToType.set(arch, [])\n        }\n        return\n      }\n\n      for (const type of types) {\n        const suffixPos = type.lastIndexOf(\":\")\n        if (suffixPos > 0) {\n          addValue(archToType, archFromString(type.substring(suffixPos + 1)), type.substring(0, suffixPos))\n        } else {\n          for (const arch of commonArch(true)) {\n            addValue(archToType, arch, type)\n          }\n        }\n      }\n    }\n\n    if (options.mac != null) {\n      processTargets(Platform.MAC, options.mac)\n    }\n    if (options.linux != null) {\n      processTargets(Platform.LINUX, options.linux)\n    }\n    if (options.win != null) {\n      processTargets(Platform.WINDOWS, options.win)\n    }\n\n    this.projectDir = options.projectDir == null ? process.cwd() : path.resolve(options.projectDir)\n    this._appDir = this.projectDir\n    this.options = {\n      ...options,\n      prepackaged: options.prepackaged == null ? null : path.resolve(this.projectDir, options.prepackaged),\n    }\n\n    log.info({ version: PACKAGE_VERSION, os: getOsRelease() }, \"electron-builder\")\n  }\n\n  async addPackagerEventHandlers() {\n    const { type } = this.appInfo\n    this.eventEmitter.on(\"artifactBuildStarted\", await resolveFunction(type, this.config.artifactBuildStarted, \"artifactBuildStarted\"), \"user\")\n    this.eventEmitter.on(\"artifactBuildCompleted\", await resolveFunction(type, this.config.artifactBuildCompleted, \"artifactBuildCompleted\"), \"user\")\n\n    this.eventEmitter.on(\"appxManifestCreated\", await resolveFunction(type, this.config.appxManifestCreated, \"appxManifestCreated\"), \"user\")\n    this.eventEmitter.on(\"msiProjectCreated\", await resolveFunction(type, this.config.msiProjectCreated, \"msiProjectCreated\"), \"user\")\n\n    this.eventEmitter.on(\"beforePack\", await resolveFunction(type, this.config.beforePack, \"beforePack\"), \"user\")\n    this.eventEmitter.on(\"afterExtract\", await resolveFunction(type, this.config.afterExtract, \"afterExtract\"), \"user\")\n    this.eventEmitter.on(\"afterPack\", await resolveFunction(type, this.config.afterPack, \"afterPack\"), \"user\")\n    this.eventEmitter.on(\"afterSign\", await resolveFunction(type, this.config.afterSign, \"afterSign\"), \"user\")\n  }\n\n  onAfterPack(handler: PackagerEvents[\"afterPack\"]): Packager {\n    this.eventEmitter.on(\"afterPack\", handler)\n    return this\n  }\n\n  onArtifactCreated(handler: PackagerEvents[\"artifactCreated\"]): Packager {\n    this.eventEmitter.on(\"artifactCreated\", handler)\n    return this\n  }\n\n  filterPackagerEventListeners(event: keyof PackagerEvents, type: HandlerType | undefined) {\n    return this.eventEmitter.filterListeners(event, type)\n  }\n\n  clearPackagerEventListeners() {\n    this.eventEmitter.clear()\n  }\n\n  async emitArtifactBuildStarted(event: ArtifactBuildStarted, logFields?: any) {\n    log.info(\n      logFields || {\n        target: event.targetPresentableName,\n        arch: event.arch == null ? null : Arch[event.arch],\n        file: log.filePath(event.file),\n      },\n      \"building\"\n    )\n    await this.eventEmitter.emit(\"artifactBuildStarted\", event)\n  }\n\n  /**\n   * Only for sub artifacts (update info), for main artifacts use `callArtifactBuildCompleted`.\n   */\n  async emitArtifactCreated(event: ArtifactCreated) {\n    await this.eventEmitter.emit(\"artifactCreated\", event)\n  }\n\n  async emitArtifactBuildCompleted(event: ArtifactCreated) {\n    await this.eventEmitter.emit(\"artifactBuildCompleted\", event)\n    await this.emitArtifactCreated(event)\n  }\n\n  async emitAppxManifestCreated(path: string) {\n    await this.eventEmitter.emit(\"appxManifestCreated\", path)\n  }\n\n  async emitMsiProjectCreated(path: string) {\n    await this.eventEmitter.emit(\"msiProjectCreated\", path)\n  }\n\n  async emitBeforePack(context: BeforePackContext) {\n    await this.eventEmitter.emit(\"beforePack\", context)\n  }\n\n  async emitAfterPack(context: AfterPackContext) {\n    await this.eventEmitter.emit(\"afterPack\", context)\n  }\n\n  async emitAfterSign(context: AfterPackContext) {\n    await this.eventEmitter.emit(\"afterSign\", context)\n  }\n\n  async emitAfterExtract(context: AfterPackContext) {\n    await this.eventEmitter.emit(\"afterExtract\", context)\n  }\n\n  async validateConfig(): Promise<void> {\n    let configPath: string | null = null\n    let configFromOptions = this.options.config\n    if (typeof configFromOptions === \"string\") {\n      // it is a path to config file\n      configPath = configFromOptions\n      configFromOptions = null\n    } else if (configFromOptions != null && typeof configFromOptions.extends === \"string\" && configFromOptions.extends.includes(\".\")) {\n      configPath = configFromOptions.extends\n      delete configFromOptions.extends\n    }\n\n    const projectDir = this.projectDir\n\n    const devPackageFile = path.join(projectDir, \"package.json\")\n    this._devMetadata = await orNullIfFileNotExist(readPackageJson(devPackageFile))\n\n    const devMetadata = this.devMetadata\n    const configuration = await getConfig(projectDir, configPath, configFromOptions, new Lazy(() => Promise.resolve(devMetadata)))\n    if (log.isDebugEnabled) {\n      log.debug({ config: getSafeEffectiveConfig(configuration) }, \"effective config\")\n    }\n\n    this._appDir = await computeDefaultAppDirectory(projectDir, configuration.directories!.app)\n    this.isTwoPackageJsonProjectLayoutUsed = this._appDir !== projectDir\n\n    const appPackageFile = this.isTwoPackageJsonProjectLayoutUsed ? path.join(this.appDir, \"package.json\") : devPackageFile\n\n    // tslint:disable:prefer-conditional-expression\n    if (this.devMetadata != null && !this.isTwoPackageJsonProjectLayoutUsed) {\n      this._metadata = this.devMetadata\n    } else {\n      this._metadata = await this.readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile)\n    }\n    deepAssign(this.metadata, configuration.extraMetadata)\n\n    if (this.isTwoPackageJsonProjectLayoutUsed) {\n      log.debug({ devPackageFile, appPackageFile }, \"two package.json structure is used\")\n    }\n    checkMetadata(this.metadata, this.devMetadata, appPackageFile, devPackageFile)\n\n    await validateConfiguration(configuration, this.debugLogger)\n\n    this._configuration = configuration\n    this._devMetadata = devMetadata\n  }\n\n  // external caller of this method always uses isTwoPackageJsonProjectLayoutUsed=false and appDir=projectDir, no way (and need) to use another values\n  async build(repositoryInfo?: SourceRepositoryInfo): Promise<BuildResult> {\n    await this.validateConfig()\n\n    if (repositoryInfo != null) {\n      this._repositoryInfo.value = Promise.resolve(repositoryInfo)\n    }\n\n    this._appInfo = new AppInfo(this, null)\n    await this.addPackagerEventHandlers()\n\n    this._framework = await createFrameworkInfo(this.config, this)\n\n    const commonOutDirWithoutPossibleOsMacro = path.resolve(\n      this.projectDir,\n      expandMacro(this.config.directories!.output!, null, this._appInfo, {\n        os: \"\",\n      })\n    )\n\n    if (!isCI && (process.stdout as any).isTTY) {\n      const effectiveConfigFile = path.join(commonOutDirWithoutPossibleOsMacro, \"builder-effective-config.yaml\")\n      log.info({ file: log.filePath(effectiveConfigFile) }, \"writing effective config\")\n      await outputFile(effectiveConfigFile, getSafeEffectiveConfig(this.config))\n    }\n\n    // because artifact event maybe dispatched several times for different publish providers\n    const artifactPaths = new Set<string>()\n    this.onArtifactCreated(event => {\n      if (event.file != null) {\n        artifactPaths.add(event.file)\n      }\n    })\n\n    this.disposeOnBuildFinish(() => this.tempDirManager.cleanup())\n    const platformToTargets = await executeFinally(this.doBuild(), async () => {\n      if (this.debugLogger.isEnabled) {\n        await this.debugLogger.save(path.join(commonOutDirWithoutPossibleOsMacro, \"builder-debug.yml\"))\n      }\n\n      const toDispose = this.toDispose.slice()\n      this.toDispose.length = 0\n      for (const disposer of toDispose) {\n        await disposer().catch((e: any) => {\n          log.warn({ error: e }, \"cannot dispose\")\n        })\n      }\n    })\n\n    return {\n      outDir: commonOutDirWithoutPossibleOsMacro,\n      artifactPaths: Array.from(artifactPaths),\n      platformToTargets,\n      configuration: this.config,\n    }\n  }\n\n  private async readProjectMetadataIfTwoPackageStructureOrPrepacked(appPackageFile: string): Promise<Metadata> {\n    let data = await orNullIfFileNotExist(readPackageJson(appPackageFile))\n    if (data != null) {\n      return data\n    }\n\n    data = await orNullIfFileNotExist(readAsarJson(path.join(this.projectDir, \"app.asar\"), \"package.json\"))\n    if (data != null) {\n      this._isPrepackedAppAsar = true\n      return data\n    }\n\n    throw new Error(`Cannot find package.json in the ${path.dirname(appPackageFile)}`)\n  }\n\n  private async doBuild(): Promise<Map<Platform, Map<string, Target>>> {\n    const taskManager = new AsyncTaskManager(this.cancellationToken)\n    const syncTargetsIfAny = [] as Target[]\n\n    const platformToTarget = new Map<Platform, Map<string, Target>>()\n    const createdOutDirs = new Set<string>()\n\n    for (const [platform, archToType] of this.options.targets!) {\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      if (platform === Platform.MAC && process.platform === Platform.WINDOWS.nodeName) {\n        throw new InvalidConfigurationError(\"Build for macOS is supported only on macOS, please see https://electron.build/multi-platform-build\")\n      }\n\n      const packager = await this.createHelper(platform)\n      const nameToTarget: Map<string, Target> = new Map()\n      platformToTarget.set(platform, nameToTarget)\n\n      for (const [arch, targetNames] of computeArchToTargetNamesMap(archToType, packager, platform)) {\n        if (this.cancellationToken.cancelled) {\n          break\n        }\n\n        // support os and arch macro in output value\n        const outDir = path.resolve(this.projectDir, packager.expandMacro(this.config.directories!.output!, Arch[arch]))\n        const targetList = createTargets(nameToTarget, targetNames.length === 0 ? packager.defaultTarget : targetNames, outDir, packager)\n        await createOutDirIfNeed(targetList, createdOutDirs)\n        await packager.pack(outDir, arch, targetList, taskManager)\n      }\n\n      if (this.cancellationToken.cancelled) {\n        break\n      }\n\n      for (const target of nameToTarget.values()) {\n        if (target.isAsyncSupported) {\n          taskManager.addTask(target.finishBuild())\n        } else {\n          syncTargetsIfAny.push(target)\n        }\n      }\n    }\n\n    await taskManager.awaitTasks()\n\n    for (const target of syncTargetsIfAny) {\n      await target.finishBuild()\n    }\n    return platformToTarget\n  }\n\n  private async createHelper(platform: Platform): Promise<PlatformPackager<any>> {\n    if (this.options.platformPackagerFactory != null) {\n      return this.options.platformPackagerFactory(this, platform)\n    }\n\n    switch (platform) {\n      case Platform.MAC: {\n        const helperClass = (await import(\"./macPackager\")).MacPackager\n        return new helperClass(this)\n      }\n\n      case Platform.WINDOWS: {\n        const helperClass = (await import(\"./winPackager\")).WinPackager\n        return new helperClass(this)\n      }\n\n      case Platform.LINUX:\n        return new (await import(\"./linuxPackager\")).LinuxPackager(this)\n\n      default:\n        throw new Error(`Unknown platform: ${platform}`)\n    }\n  }\n\n  public async installAppDependencies(platform: Platform, arch: Arch): Promise<any> {\n    if (this.options.prepackaged != null || !this.framework.isNpmRebuildRequired) {\n      return\n    }\n\n    const frameworkInfo = { version: this.framework.version, useCustomDist: true }\n    const config = this.config\n    if (config.nodeGypRebuild === true) {\n      await nodeGypRebuild(platform.nodeName, Arch[arch], frameworkInfo)\n    }\n\n    if (config.npmRebuild === false) {\n      log.info({ reason: \"npmRebuild is set to false\" }, \"skipped dependencies rebuild\")\n      return\n    }\n\n    const beforeBuild = await resolveFunction(this.appInfo.type, config.beforeBuild, \"beforeBuild\")\n    if (beforeBuild != null) {\n      const performDependenciesInstallOrRebuild = await beforeBuild({\n        appDir: this.appDir,\n        electronVersion: this.config.electronVersion!,\n        platform,\n        arch: Arch[arch],\n      })\n\n      // If beforeBuild resolves to false, it means that handling node_modules is done outside of electron-builder.\n      this._nodeModulesHandledExternally = !performDependenciesInstallOrRebuild\n      if (!performDependenciesInstallOrRebuild) {\n        return\n      }\n    }\n\n    if (config.buildDependenciesFromSource === true && platform.nodeName !== process.platform) {\n      log.info({ reason: \"platform is different and buildDependenciesFromSource is set to true\" }, \"skipped dependencies rebuild\")\n    } else {\n      await installOrRebuild(config, this.appDir, {\n        frameworkInfo,\n        platform: platform.nodeName,\n        arch: Arch[arch],\n        productionDeps: this.getNodeDependencyInfo(null, false) as Lazy<Array<NodeModuleDirInfo>>,\n      })\n    }\n  }\n}\n\nfunction createOutDirIfNeed(targetList: Array<Target>, createdOutDirs: Set<string>): Promise<any> {\n  const ourDirs = new Set<string>()\n  for (const target of targetList) {\n    // noinspection SuspiciousInstanceOfGuard\n    if (target instanceof NoOpTarget) {\n      continue\n    }\n\n    const outDir = target.outDir\n    if (!createdOutDirs.has(outDir)) {\n      ourDirs.add(outDir)\n    }\n  }\n\n  if (ourDirs.size === 0) {\n    return Promise.resolve()\n  }\n\n  return Promise.all(\n    Array.from(ourDirs)\n      .sort()\n      .map(dir => {\n        return mkdirs(dir)\n          .then(() => chmod(dir, 0o755) /* set explicitly */)\n          .then(() => createdOutDirs.add(dir))\n      })\n  )\n}\n\nexport interface BuildResult {\n  readonly outDir: string\n  readonly artifactPaths: Array<string>\n  readonly platformToTargets: Map<Platform, Map<string, Target>>\n  readonly configuration: Configuration\n}\n\nfunction getSafeEffectiveConfig(configuration: Configuration): string {\n  const o = JSON.parse(safeStringifyJson(configuration))\n  if (o.cscLink != null) {\n    o.cscLink = \"<hidden by builder>\"\n  }\n  return serializeToYaml(o, true)\n}\n"]}