{"version": 3, "file": "PwshVm.js", "sourceRoot": "", "sources": ["../../src/vm/PwshVm.ts"], "names": [], "mappings": ";;;AAAA,+CAAkC;AAClC,uCAA+B;AAC/B,6BAAiD;AAEjD,MAAa,aAAc,SAAQ,cAAS;IAC1C;QACE,KAAK,EAAE,CAAA;QAGA,sBAAiB,GAAG,IAAI,eAAI,CAAS,KAAK,IAAI,EAAE;YACvD,kBAAG,CAAC,IAAI,CAAC,IAAI,EAAE,oCAAoC,CAAC,CAAA;YACpD,IAAI,MAAM,oBAAe,CAAC,KAAK,EAAE,CAAC;gBAChC,OAAO,MAAM,CAAA;YACf,CAAC;YACD,MAAM,YAAY,GAAG,yEAAyE,CAAA;YAC9F,kBAAG,CAAC,KAAK,CACP;gBACE,GAAG,EAAE,+FAA+F;gBACpG,KAAK,EAAE,+FAA+F;aACvG,EACD,YAAY,CACb,CAAA;YACD,MAAM,IAAI,KAAK,CAAC,YAAY,CAAC,CAAA;QAC/B,CAAC,CAAC,CAAA;IAhBF,CAAC;CAiBF;AApBD,sCAoBC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { Lazy } from \"lazy-val\"\nimport { isPwshAvailable, VmManager } from \"./vm\"\n\nexport class PwshVmManager extends VmManager {\n  constructor() {\n    super()\n  }\n\n  readonly powershellCommand = new Lazy<string>(async () => {\n    log.info(null, \"checking for `pwsh` for powershell\")\n    if (await isPwshAvailable.value) {\n      return \"pwsh\"\n    }\n    const errorMessage = `unable to find \\`pwsh\\`, please install per instructions linked in logs`\n    log.error(\n      {\n        mac: \"https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell-on-macos\",\n        linux: \"https://learn.microsoft.com/en-us/powershell/scripting/install/installing-powershell-on-linux\",\n      },\n      errorMessage\n    )\n    throw new Error(errorMessage)\n  })\n}\n"]}