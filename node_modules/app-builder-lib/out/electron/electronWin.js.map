{"version": 3, "file": "electronWin.js", "sourceRoot": "", "sources": ["../../src/electron/electronWin.ts"], "names": [], "mappings": ";;AAOA,kDAkCC;AAzCD,+CAAkC;AAClC,0CAAiD;AACjD,6BAA4B;AAC5B,qCAAsE;AAGtE,gBAAgB;AACT,KAAK,UAAU,mBAAmB,CAAC,cAAsB,EAAE,aAA4B;IAC5F,MAAM,MAAM,GAAG,MAAM,IAAA,mBAAQ,EAAC,cAAc,CAAC,CAAA;IAC7C,MAAM,UAAU,GAAG,sBAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;IAC5C,MAAM,QAAQ,GAAG,8BAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAEtD,MAAM,WAAW,GAAG,kBAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;IACtE,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC7B,MAAM,IAAI,KAAK,CAAC,mCAAmC,cAAc,EAAE,CAAC,CAAA;IACtE,CAAC;IAED,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAA;IACjE,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,iCAAiC,cAAc,EAAE,CAAC,CAAA;IACpE,CAAC;IAED,8GAA8G;IAC9G,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;QAChH,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;QAChC,GAAG;QACH,KAAK;KACN,CAAC,CAAC,CAAA;IAEH,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;QACpB,IAAI,EAAE,WAAW;QACjB,EAAE,EAAE,cAAc;QAClB,GAAG,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;QAC/C,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;QACvB,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ;KAChC,CAAC,CAAA;IAEF,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;IAEnC,MAAM,IAAA,oBAAS,EAAC,cAAc,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAA;IACnE,kBAAG,CAAC,IAAI,CAAC,EAAE,cAAc,EAAE,kBAAG,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,EAAE,6CAA6C,CAAC,CAAA;AAC3G,CAAC", "sourcesContent": ["import { log } from \"builder-util\"\nimport { readFile, writeFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { NtExecutable, NtExecutableResource, Resource } from \"resedit\"\nimport { AsarIntegrity } from \"../asar/integrity\"\n\n/** @internal */\nexport async function addWinAsarIntegrity(executablePath: string, asarIntegrity: AsarIntegrity) {\n  const buffer = await readFile(executablePath)\n  const executable = NtExecutable.from(buffer)\n  const resource = NtExecutableResource.from(executable)\n\n  const versionInfo = Resource.VersionInfo.fromEntries(resource.entries)\n  if (versionInfo.length !== 1) {\n    throw new Error(`Failed to parse version info in ${executablePath}`)\n  }\n\n  const languages = versionInfo[0].getAllLanguagesForStringValues()\n  if (languages.length !== 1) {\n    throw new Error(`Failed to locate languages in ${executablePath}`)\n  }\n\n  // See: https://github.com/electron/packager/blob/00d20b99cf4aa4621103dbbd09ff7de7d2f7f539/src/resedit.ts#L124\n  const integrityList = Array.from(Object.entries(asarIntegrity)).map(([file, { algorithm: alg, hash: value }]) => ({\n    file: path.win32.normalize(file),\n    alg,\n    value,\n  }))\n\n  resource.entries.push({\n    type: \"INTEGRITY\",\n    id: \"ELECTRONASAR\",\n    bin: Buffer.from(JSON.stringify(integrityList)),\n    lang: languages[0].lang,\n    codepage: languages[0].codepage,\n  })\n\n  resource.outputResource(executable)\n\n  await writeFile(executablePath, Buffer.from(executable.generate()))\n  log.info({ executablePath: log.filePath(executablePath) }, \"updating asar integrity executable resource\")\n}\n"]}