{"version": 3, "file": "electronVersion.js", "sourceRoot": "", "sources": ["../../src/electron/electronVersion.ts"], "names": [], "mappings": ";;AAgBA,gDAQC;AAED,0EAWC;AAED,gDAWC;AAGD,wDA0DC;AA/GD,uEAAwE;AAExE,+CAA2E;AAC3E,+DAA+C;AAC/C,uCAAmC;AAEnC,6BAA4B;AAC5B,iCAAgC;AAEhC,kDAAiD;AACjD,8CAA0D;AAI1D,MAAM,gBAAgB,GAAG,CAAC,UAAU,EAAE,mBAAmB,EAAE,2BAA2B,EAAE,kBAAkB,CAAC,CAAA;AAEpG,KAAK,UAAU,kBAAkB,CAAC,UAAkB,EAAE,MAAsB;IACjF,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,MAAM,GAAG,MAAM,IAAA,kBAAS,EAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAClD,CAAC;IACD,IAAI,MAAM,CAAC,eAAe,IAAI,IAAI,EAAE,CAAC;QACnC,OAAO,MAAM,CAAC,eAAe,CAAA;IAC/B,CAAC;IACD,OAAO,sBAAsB,CAAC,UAAU,CAAC,CAAA;AAC3C,CAAC;AAEM,KAAK,UAAU,+BAA+B,CAAC,UAAkB;IACtE,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,OAAO,CAAC,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;QAC9F,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,2CAA2C,CAAC,CAAA;YAC3E,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAEM,KAAK,UAAU,kBAAkB,CAAC,UAAkB;IACzD,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC,CAAA;QACpF,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,IAAI,CAAC,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACxB,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,sCAAsC,CAAC,CAAA;YACtE,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC;AAED,gBAAgB;AACT,KAAK,UAAU,sBAAsB,CAAC,UAAkB;IAC7D,MAAM,MAAM,GAAG,MAAM,+BAA+B,CAAC,UAAU,CAAC,CAAA;IAChE,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;QACnB,OAAO,MAAM,CAAA;IACf,CAAC;IAED,MAAM,iBAAiB,GAAG,CAAC,UAAU,EAAE,MAAM,IAAA,kCAAkB,EAAC,UAAU,CAAC,CAAC,CAAA;IAC5E,IAAI,UAAU,GAA0B,IAAI,CAAA;IAC5C,KAAK,MAAM,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACpC,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAoB,EAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC,CAAC,CAAA;QACrF,UAAU,GAAG,QAAQ,CAAC,CAAC,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;QAChE,IAAI,UAAU,EAAE,CAAC;YACf,MAAK;QACP,CAAC;IACH,CAAC;IACD,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,IAAI,MAAK,kBAAkB,EAAE,CAAC;QAC5C,kBAAG,CAAC,IAAI,CAAC,+FAA+F,CAAC,CAAA;QACzG,MAAM,OAAO,GAAG,MAAM,2BAAY,CAAC,OAAO,CAAC;YACzC,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,mCAAmC;YACzC,OAAO,EAAE;gBACP,MAAM,EAAE,sDAAsD;aAC/D;SACF,CAAC,CAAA;QACF,MAAM,IAAI,GAAG,IAAA,+BAAQ,EAAC,OAAQ,CAAC,CAAA;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,iCAAiC,CAAC,CAAA;QACrF,MAAM,CAAC,GAAG,mBAAmB,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAC,CAAC,CAAC,CAAA;QACvF,OAAO,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC/C,CAAC;SAAM,IAAI,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,MAAK,QAAQ,EAAE,CAAC;QAC5C,kBAAG,CAAC,IAAI,CAAC,6GAA6G,CAAC,CAAA;QACvH,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAC5B,CAAC,MAAM,2BAAY,CAAC,OAAO,CAAC;gBAC1B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,aAAa,UAAU,CAAC,IAAI,KAAK,kBAAkB,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,UAAU,kBAAkB;gBACtG,OAAO,EAAE;oBACP,MAAM,EAAE,kBAAkB;iBAC3B;aACF,CAAC,CAAE,CACL,CAAA;YACD,MAAM,OAAO,GAAG,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,QAAQ,CAAA;YAC/G,kBAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,WAAW,UAAU,CAAC,IAAI,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC,CAAA;YACzE,OAAO,OAAO,CAAA;QAChB,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,kBAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QACb,CAAC;QAED,MAAM,IAAI,wCAAyB,CAAC,mEAAmE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,GAAG,CAAC,CAAA;IAClJ,CAAC;IACD,MAAM,OAAO,GAAG,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,OAAO,CAAA;IACnC,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,kBAAkB,OAAO,4BAA4B,CAAA;QACnG,MAAM,IAAI,wCAAyB,CACjC,oHAAoH,cAAc,iGAAiG,CACpO,CAAA;IACH,CAAC;IAED,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAE,CAAC,MAAM,EAAE,CAAA;AACzC,CAAC;AAOD,SAAS,uBAAuB,CAAC,WAAgB;IAC/C,KAAK,MAAM,IAAI,IAAI,gBAAgB,EAAE,CAAC;QACpC,MAAM,eAAe,GAAG,WAAW,CAAC,eAAe,CAAA;QACnD,IAAI,GAAG,GAAG,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAChE,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,MAAM,YAAY,GAAG,WAAW,CAAC,YAAY,CAAA;YAC7C,GAAG,GAAG,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;QACxD,CAAC;QACD,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;YAChB,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,CAAA;QAC/B,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import { getProjectRootPath } from \"@electron/rebuild/lib/search-module\"\n\nimport { httpExecutor, InvalidConfigurationError, log } from \"builder-util\"\nimport { parseXml } from \"builder-util-runtime\"\nimport { readJson } from \"fs-extra\"\nimport { <PERSON>zy } from \"lazy-val\"\nimport * as path from \"path\"\nimport * as semver from \"semver\"\nimport { Configuration } from \"../configuration\"\nimport { getConfig } from \"../util/config/config\"\nimport { orNullIfFileNotExist } from \"../util/config/load\"\n\nexport type MetadataValue = Lazy<Record<string, any> | null>\n\nconst electronPackages = [\"electron\", \"electron-prebuilt\", \"electron-prebuilt-compile\", \"electron-nightly\"]\n\nexport async function getElectronVersion(projectDir: string, config?: Configuration): Promise<string> {\n  if (config == null) {\n    config = await getConfig(projectDir, null, null)\n  }\n  if (config.electronVersion != null) {\n    return config.electronVersion\n  }\n  return computeElectronVersion(projectDir)\n}\n\nexport async function getElectronVersionFromInstalled(projectDir: string): Promise<string | null> {\n  for (const name of electronPackages) {\n    try {\n      return (await readJson(path.join(projectDir, \"node_modules\", name, \"package.json\"))).version\n    } catch (e: any) {\n      if (e.code !== \"ENOENT\") {\n        log.warn({ name, error: e }, `cannot read electron version package.json`)\n      }\n    }\n  }\n  return null\n}\n\nexport async function getElectronPackage(projectDir: string) {\n  for (const name of electronPackages) {\n    try {\n      return await readJson(path.join(projectDir, \"node_modules\", name, \"package.json\"))\n    } catch (e: any) {\n      if (e.code !== \"ENOENT\") {\n        log.warn({ name, error: e }, `cannot find electron in package.json`)\n      }\n    }\n  }\n  return null\n}\n\n/** @internal */\nexport async function computeElectronVersion(projectDir: string): Promise<string> {\n  const result = await getElectronVersionFromInstalled(projectDir)\n  if (result != null) {\n    return result\n  }\n\n  const potentialRootDirs = [projectDir, await getProjectRootPath(projectDir)]\n  let dependency: NameAndVersion | null = null\n  for (const dir of potentialRootDirs) {\n    const metadata = await orNullIfFileNotExist(readJson(path.join(dir, \"package.json\")))\n    dependency = metadata ? findFromPackageMetadata(metadata) : null\n    if (dependency) {\n      break\n    }\n  }\n  if (dependency?.name === \"electron-nightly\") {\n    log.info(\"You are using a nightly version of electron, be warned that those builds are highly unstable.\")\n    const feedXml = await httpExecutor.request({\n      hostname: \"github.com\",\n      path: `/electron/nightlies/releases.atom`,\n      headers: {\n        accept: \"application/xml, application/atom+xml, text/xml, */*\",\n      },\n    })\n    const feed = parseXml(feedXml!)\n    const latestRelease = feed.element(\"entry\", false, `No published versions on GitHub`)\n    const v = /\\/tag\\/v?([^/]+)$/.exec(latestRelease.element(\"link\").attribute(\"href\"))![1]\n    return v.startsWith(\"v\") ? v.substring(1) : v\n  } else if (dependency?.version === \"latest\") {\n    log.warn('Electron version is set to \"latest\", but it is recommended to set it to some more restricted version range.')\n    try {\n      const releaseInfo = JSON.parse(\n        (await httpExecutor.request({\n          hostname: \"github.com\",\n          path: `/electron/${dependency.name === \"electron-nightly\" ? \"nightlies\" : \"electron\"}/releases/latest`,\n          headers: {\n            accept: \"application/json\",\n          },\n        }))!\n      )\n      const version = releaseInfo.tag_name.startsWith(\"v\") ? releaseInfo.tag_name.substring(1) : releaseInfo.tag_name\n      log.info({ version }, `resolve ${dependency.name}@${dependency.version}`)\n      return version\n    } catch (e: any) {\n      log.warn(e)\n    }\n\n    throw new InvalidConfigurationError(`Cannot find electron dependency to get electron version in the '${path.join(projectDir, \"package.json\")}'`)\n  }\n  const version = dependency?.version\n  if (version == null || !/^\\d/.test(version)) {\n    const versionMessage = version == null ? \"\" : ` and version (\"${version}\") is not fixed in project`\n    throw new InvalidConfigurationError(\n      `Cannot compute electron version from installed node modules - none of the possible electron modules are installed${versionMessage}.\\nSee https://github.com/electron-userland/electron-builder/issues/3984#issuecomment-504968246`\n    )\n  }\n\n  return semver.coerce(version)!.format()\n}\n\ninterface NameAndVersion {\n  readonly name: string\n  readonly version: string\n}\n\nfunction findFromPackageMetadata(packageData: any): NameAndVersion | null {\n  for (const name of electronPackages) {\n    const devDependencies = packageData.devDependencies\n    let dep = devDependencies == null ? null : devDependencies[name]\n    if (dep == null) {\n      const dependencies = packageData.dependencies\n      dep = dependencies == null ? null : dependencies[name]\n    }\n    if (dep != null) {\n      return { name, version: dep }\n    }\n  }\n  return null\n}\n"]}