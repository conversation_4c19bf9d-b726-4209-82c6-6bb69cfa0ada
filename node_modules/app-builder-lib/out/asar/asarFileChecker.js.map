{"version": 3, "file": "asarFileChecker.js", "sourceRoot": "", "sources": ["../../src/asar/asarFileChecker.ts"], "names": [], "mappings": ";;AAGA,gDAiBC;AApBD,uCAAsC;AAGtC,SAAgB,kBAAkB,CAAC,QAAgB,EAAE,YAAoB,EAAE,aAAqB;IAC9F,SAAS,KAAK,CAAC,IAAY;QACzB,OAAO,IAAI,KAAK,CAAC,GAAG,aAAa,KAAK,YAAY,aAAa,QAAQ,KAAK,IAAI,EAAE,CAAC,CAAA;IACrF,CAAC;IACD,IAAI,IAAqB,CAAA;IACzB,IAAI,CAAC;QACH,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,YAAY,EAAE,KAAK,CAAC,CAAA;IACrD,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,sDAAsD,CAAC,EAAE,CAAC;YAC/E,MAAM,KAAK,CAAC,mDAAmD,CAAC,CAAA;QAClE,CAAC;QACD,MAAM,KAAK,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAA;IACnC,CAAC;IACD,IAAK,IAA4B,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;QAC7C,MAAM,KAAK,CAAC,sBAAsB,CAAC,CAAA;IACrC,CAAC;IACD,OAAO,IAAI,CAAA;AACb,CAAC", "sourcesContent": ["import * as asar from \"@electron/asar\"\nimport { FilesystemEntry, FilesystemFileEntry } from \"@electron/asar/lib/filesystem\"\n\nexport function checkFileInArchive(asarFile: string, relativeFile: string, messagePrefix: string) {\n  function error(text: string) {\n    return new Error(`${messagePrefix} \"${relativeFile}\" in the \"${asarFile}\" ${text}`)\n  }\n  let stat: FilesystemEntry\n  try {\n    stat = asar.statFile(asarFile, relativeFile, false)\n  } catch (e: any) {\n    if (e.message.includes(\"Cannot read properties of undefined (reading 'link')\")) {\n      throw error(\"does not exist. Seems like a wrong configuration.\")\n    }\n    throw error(`is corrupted: ${e}`)\n  }\n  if ((stat as FilesystemFileEntry).size === 0) {\n    throw error(`is corrupted: size 0`)\n  }\n  return stat\n}\n"]}