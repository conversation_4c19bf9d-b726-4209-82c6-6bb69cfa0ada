{"version": 3, "file": "nodeModulesCollector.js", "sourceRoot": "", "sources": ["../../src/node-module-collector/nodeModulesCollector.ts"], "names": [], "mappings": ";;;AAAA,mCAAqE;AACrE,6BAA4B;AAC5B,yBAAwB;AAExB,+CAAwC;AAGxC,MAAsB,oBAAoB;IAKxC,YAA6B,OAAe;QAAf,YAAO,GAAP,OAAO,CAAQ;QAJpC,gBAAW,GAAqB,EAAE,CAAA;QAChC,sBAAiB,GAAwB,IAAI,GAAG,EAAE,CAAA;QAClD,oBAAe,GAAmB,IAAI,GAAG,EAAE,CAAA;IAEN,CAAC;IAEzC,KAAK,CAAC,cAAc;QACzB,MAAM,IAAI,GAAM,MAAM,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAChD,MAAM,QAAQ,GAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAA;QACpD,MAAM,UAAU,GAAiC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAA;QAEnF,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAA;QAEvC,MAAM,cAAc,GAAmB,IAAI,CAAC,+BAA+B,CAAC,UAAU,CAAC,CAAA;QACvF,MAAM,eAAe,GAAoB,IAAI,CAAC,wBAAwB,CAAC,cAAc,CAAC,CAAA;QAEtF,MAAM,aAAa,GAAkB,IAAA,aAAK,EAAC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;QACrG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAA;QAElE,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAYS,KAAK,CAAC,mBAAmB;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA;QAC1C,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAA;QAC3B,MAAM,YAAY,GAAG,MAAM,IAAA,mBAAI,EAAC,OAAO,EAAE,IAAI,EAAE;YAC7C,GAAG,EAAE,IAAI,CAAC,OAAO;YACjB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAA;QACF,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAA;IACjD,CAAC;IAES,mBAAmB,CAAC,OAAU;QACtC,2EAA2E;QAC3E,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,OAAO,CAAA;QACjE,MAAM,IAAI,GAAiC;YACzC,IAAI;YACJ,OAAO;YACP,IAAI;YACJ,UAAU;YACV,sBAAsB;YACtB,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;SACjD,CAAA;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAES,eAAe,CAAC,IAAuB;QAC/C,OAAO,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC;YACzC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,CAAC,WAAW,EAAE,wBAAwB,CAAC,EAAE,EAAE;gBAC7E,OAAO;oBACL,GAAG,KAAK;oBACR,CAAC,WAAW,CAAC,EACX,OAAO,wBAAwB,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC,MAAM,GAAG,CAAC;wBAC9F,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,CAAC;wBACpD,CAAC,CAAC,wBAAwB;iBAC/B,CAAA;YACH,CAAC,EAAE,EAAE,CAAC;YACR,CAAC,CAAC,SAAS,CAAA;IACf,CAAC;IAES,WAAW,CAAC,QAAgB;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YACpC,IAAI,KAAK,CAAC,cAAc,EAAE,EAAE,CAAC;gBAC3B,OAAO,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;YAClC,CAAC;iBAAM,CAAC;gBACN,OAAO,QAAQ,CAAA;YACjB,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,kBAAG,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE,sBAAsB,CAAC,CAAA;YAC5E,OAAO,QAAQ,CAAA;QACjB,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,IAAoB,EAAE,SAAS,GAAG,GAAG;QACpE,OAAO,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,CAAC,MAAM,CAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;YACnF,MAAM,CAAC,WAAW,EAAE,YAAY,CAAC,GAAG,IAAI,CAAA;YACxC,2DAA2D;YAC3D,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC3C,OAAO,GAAG,CAAA;YACZ,CAAC;YACD,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,EAAE,CAAA;YAC1C,MAAM,MAAM,GAAG,GAAG,WAAW,IAAI,OAAO,EAAE,CAAA;YAC1C,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;gBACvB,kBAAG,CAAC,KAAK,CACP;oBACE,WAAW;oBACX,IAAI,EAAE,YAAY;oBAClB,YAAY,EAAE,IAAI,CAAC,IAAI;oBACvB,aAAa,EAAE,IAAI,CAAC,OAAO;iBAC5B,EACD,8BAA8B,CAC/B,CAAA;gBACD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAA;YAC7E,CAAC;YACD,wEAAwE;YACxE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;YACvF,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBACpB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,YAAY,EAAE,EAAE,EAAE,CAAA;YACvC,CAAC;YACD,GAAG,CAAC,SAAS,CAAC,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACxC,IAAI,IAAI,CAAC,4BAA4B,EAAE,CAAC;gBACtC,kBAAG,CAAC,KAAK,CACP;oBACE,UAAU,EAAE,WAAW;oBACvB,OAAO;oBACP,IAAI,EAAE,YAAY,CAAC,IAAI;oBACvB,YAAY,EAAE,IAAI,CAAC,IAAI;oBACvB,aAAa,EAAE,IAAI,CAAC,OAAO;iBAC5B,EACD,+BAA+B,CAChC,CAAA;gBACD,OAAO,GAAG,CAAA;YACZ,CAAC;YAED,OAAO,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,CAAA;QAC3E,CAAC,EAAE,EAAE,CAAC,CAAA;IACR,CAAC;IAEO,sBAAsB,CAAC,IAAkC;;QAC/D,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC;YACnE,IAAI,MAAM,CAAC,IAAI,CAAC,MAAA,KAAK,CAAC,YAAY,mCAAI,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,CAAA;gBAC1D,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAA;YACpC,CAAC;QACH,CAAC;IACH,CAAC;IAEO,qBAAqB,CAAC,IAAO;QACnC,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACzC,MAAM,WAAW,GAA+B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAA;YAChG,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAA;YACvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7D,IAAI,GAAG,KAAK,cAAc,EAAE,CAAC;oBAC3B,OAAO,KAAK,CAAA;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,kBAAkB,CAAC,GAAoB,EAAE,MAAc,GAAG,EAAE,QAAkC,IAAI,GAAG,EAAE;QAC7G,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QACzB,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,SAAS,CAAE,CAAC,CAAC,CAAC,CAAA;QACrC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,IAAI,GAAG;gBACL,IAAI;gBACJ,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,GAAG,CAAC,KAAK,CAAC,gBAAgB,CAAE,CAAC,CAAC,CAAC,IAAI,EAAE;gBAChD,YAAY,EAAE,IAAI,GAAG,EAAe;gBACpC,SAAS,EAAE,IAAI,GAAG,CAAS,EAAE,CAAC;aAC/B,CAAA;YACD,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAA;YAEpB,KAAK,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,YAAY,IAAI,EAAE,EAAE,CAAC;gBACtD,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC,CAAA;YACjE,CAAC;QACH,CAAC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,eAAe,CAAC,YAAgC,EAAE,MAAwB;QAChF,IAAI,YAAY,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;YAC5B,OAAM;QACR,CAAC;QAED,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,MAAM,EAAE,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;YACtC,MAAM,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,SAAS,EAAE,CAAC,CAAA;YAC9D,IAAI,CAAC,KAAK,SAAS,EAAE,CAAC;gBACpB,kBAAG,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,iCAAiC,CAAC,CAAA;gBACzE,SAAQ;YACV,CAAC;YACD,MAAM,IAAI,GAAmB;gBAC3B,IAAI,EAAE,CAAC,CAAC,IAAI;gBACZ,OAAO,EAAE,SAAS;gBAClB,GAAG,EAAE,CAAC;aACP,CAAA;YACD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACjB,IAAI,CAAC,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;gBAC5B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAA;gBACtB,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,CAAA;YACzD,CAAC;QACH,CAAC;QACD,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;IACrD,CAAC;CACF;AAvMD,oDAuMC", "sourcesContent": ["import { hoist, type HoisterTree, type HoisterResult } from \"./hoist\"\nimport * as path from \"path\"\nimport * as fs from \"fs\"\nimport type { NodeModuleInfo, DependencyTree, DependencyGraph, Dependency } from \"./types\"\nimport { exec, log } from \"builder-util\"\nimport { Lazy } from \"lazy-val\"\n\nexport abstract class NodeModulesCollector<T extends Dependency<T, OptionalsType>, OptionalsType> {\n  private nodeModules: NodeModuleInfo[] = []\n  protected dependencyPathMap: Map<string, string> = new Map()\n  protected allDependencies: Map<string, T> = new Map()\n\n  constructor(private readonly rootDir: string) {}\n\n  public async getNodeModules(): Promise<NodeModuleInfo[]> {\n    const tree: T = await this.getDependenciesTree()\n    const realTree: T = this.getTreeFromWorkspaces(tree)\n    const parsedTree: Dependency<T, OptionalsType> = this.extractRelevantData(realTree)\n\n    this.collectAllDependencies(parsedTree)\n\n    const productionTree: DependencyTree = this.extractProductionDependencyTree(parsedTree)\n    const dependencyGraph: DependencyGraph = this.convertToDependencyGraph(productionTree)\n\n    const hoisterResult: HoisterResult = hoist(this.transToHoisterTree(dependencyGraph), { check: true })\n    this._getNodeModules(hoisterResult.dependencies, this.nodeModules)\n\n    return this.nodeModules\n  }\n\n  public abstract readonly installOptions: Promise<{\n    cmd: string\n    args: string[]\n    lockfile: string\n  }>\n  protected abstract readonly pmCommand: Lazy<string>\n  protected abstract getArgs(): string[]\n  protected abstract parseDependenciesTree(jsonBlob: string): T\n  protected abstract extractProductionDependencyTree(tree: Dependency<T, OptionalsType>): DependencyTree\n\n  protected async getDependenciesTree(): Promise<T> {\n    const command = await this.pmCommand.value\n    const args = this.getArgs()\n    const dependencies = await exec(command, args, {\n      cwd: this.rootDir,\n      shell: true,\n    })\n    return this.parseDependenciesTree(dependencies)\n  }\n\n  protected extractRelevantData(npmTree: T): Dependency<T, OptionalsType> {\n    // Do not use `...npmTree` as we are explicitly extracting the data we need\n    const { name, version, path, workspaces, dependencies } = npmTree\n    const tree: Dependency<T, OptionalsType> = {\n      name,\n      version,\n      path,\n      workspaces,\n      // DFS extract subtree\n      dependencies: this.extractInternal(dependencies),\n    }\n\n    return tree\n  }\n\n  protected extractInternal(deps: T[\"dependencies\"]): T[\"dependencies\"] {\n    return deps && Object.keys(deps).length > 0\n      ? Object.entries(deps).reduce((accum, [packageName, depObjectOrVersionString]) => {\n          return {\n            ...accum,\n            [packageName]:\n              typeof depObjectOrVersionString === \"object\" && Object.keys(depObjectOrVersionString).length > 0\n                ? this.extractRelevantData(depObjectOrVersionString)\n                : depObjectOrVersionString,\n          }\n        }, {})\n      : undefined\n  }\n\n  protected resolvePath(filePath: string): string {\n    try {\n      const stats = fs.lstatSync(filePath)\n      if (stats.isSymbolicLink()) {\n        return fs.realpathSync(filePath)\n      } else {\n        return filePath\n      }\n    } catch (error: any) {\n      log.debug({ message: error.message || error.stack }, \"error resolving path\")\n      return filePath\n    }\n  }\n\n  private convertToDependencyGraph(tree: DependencyTree, parentKey = \".\"): DependencyGraph {\n    return Object.entries(tree.dependencies || {}).reduce<DependencyGraph>((acc, curr) => {\n      const [packageName, dependencies] = curr\n      // Skip empty dependencies (like some optionalDependencies)\n      if (Object.keys(dependencies).length === 0) {\n        return acc\n      }\n      const version = dependencies.version || \"\"\n      const newKey = `${packageName}@${version}`\n      if (!dependencies.path) {\n        log.error(\n          {\n            packageName,\n            data: dependencies,\n            parentModule: tree.name,\n            parentVersion: tree.version,\n          },\n          \"dependency path is undefined\"\n        )\n        throw new Error(\"unable to parse `path` during `tree.dependencies` reduce\")\n      }\n      // Map dependency details: name, version and path to the dependency tree\n      this.dependencyPathMap.set(newKey, path.normalize(this.resolvePath(dependencies.path)))\n      if (!acc[parentKey]) {\n        acc[parentKey] = { dependencies: [] }\n      }\n      acc[parentKey].dependencies.push(newKey)\n      if (tree.implicitDependenciesInjected) {\n        log.debug(\n          {\n            dependency: packageName,\n            version,\n            path: dependencies.path,\n            parentModule: tree.name,\n            parentVersion: tree.version,\n          },\n          \"converted implicit dependency\"\n        )\n        return acc\n      }\n\n      return { ...acc, ...this.convertToDependencyGraph(dependencies, newKey) }\n    }, {})\n  }\n\n  private collectAllDependencies(tree: Dependency<T, OptionalsType>) {\n    for (const [key, value] of Object.entries(tree.dependencies || {})) {\n      if (Object.keys(value.dependencies ?? {}).length > 0) {\n        this.allDependencies.set(`${key}@${value.version}`, value)\n        this.collectAllDependencies(value)\n      }\n    }\n  }\n\n  private getTreeFromWorkspaces(tree: T): T {\n    if (tree.workspaces && tree.dependencies) {\n      const packageJson: Dependency<string, string> = require(path.join(this.rootDir, \"package.json\"))\n      const dependencyName = packageJson.name\n      for (const [key, value] of Object.entries(tree.dependencies)) {\n        if (key === dependencyName) {\n          return value\n        }\n      }\n    }\n\n    return tree\n  }\n\n  private transToHoisterTree(obj: DependencyGraph, key: string = `.`, nodes: Map<string, HoisterTree> = new Map()): HoisterTree {\n    let node = nodes.get(key)\n    const name = key.match(/@?[^@]+/)![0]\n    if (!node) {\n      node = {\n        name,\n        identName: name,\n        reference: key.match(/@?[^@]+@?(.+)?/)![1] || ``,\n        dependencies: new Set<HoisterTree>(),\n        peerNames: new Set<string>([]),\n      }\n      nodes.set(key, node)\n\n      for (const dep of (obj[key] || {}).dependencies || []) {\n        node.dependencies.add(this.transToHoisterTree(obj, dep, nodes))\n      }\n    }\n    return node\n  }\n\n  private _getNodeModules(dependencies: Set<HoisterResult>, result: NodeModuleInfo[]) {\n    if (dependencies.size === 0) {\n      return\n    }\n\n    for (const d of dependencies.values()) {\n      const reference = [...d.references][0]\n      const p = this.dependencyPathMap.get(`${d.name}@${reference}`)\n      if (p === undefined) {\n        log.debug({ name: d.name, reference }, \"cannot find path for dependency\")\n        continue\n      }\n      const node: NodeModuleInfo = {\n        name: d.name,\n        version: reference,\n        dir: p,\n      }\n      result.push(node)\n      if (d.dependencies.size > 0) {\n        node.dependencies = []\n        this._getNodeModules(d.dependencies, node.dependencies)\n      }\n    }\n    result.sort((a, b) => a.name.localeCompare(b.name))\n  }\n}\n"]}