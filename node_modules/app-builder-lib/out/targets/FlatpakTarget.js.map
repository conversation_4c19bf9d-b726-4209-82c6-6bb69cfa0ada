{"version": 3, "file": "FlatpakTarget.js", "sourceRoot": "", "sources": ["../../src/targets/FlatpakTarget.ts"], "names": [], "mappings": ";;AAAA,6DAA8G;AAC9G,+CAAgE;AAChE,uCAA4C;AAC5C,6BAA4B;AAC5B,kCAAgC;AAGhC,6CAA4D;AAE5D,6CAAuD;AAEvD,MAAqB,aAAc,SAAQ,aAAM;IAM/C,YACE,IAAY,EACK,QAAuB,EAChC,MAAyB,EACxB,MAAc;QAEvB,KAAK,CAAC,IAAI,CAAC,CAAA;QAJM,aAAQ,GAAR,QAAQ,CAAe;QAChC,WAAM,GAAN,MAAM,CAAmB;QACxB,WAAM,GAAN,MAAM,CAAQ;QAThB,YAAO,GAAmB;YACjC,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B;YAC7C,GAAI,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,IAAI,CAAC,IAAI,CAAC;SAC5C,CAAA;IASD,CAAC;IAED,IAAI,KAAK;QACP,OAAO,0BAA0B,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;IAC7D,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;QACvC,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,IAAI,CAAA;QAClC,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,OAAO,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;QACnG,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,SAAS;YAChC,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;QAEjD,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,QAAQ,CAAC,GAAG,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;QAC7G,MAAM,IAAA,wBAAa,EAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;QAE3C,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAA;QAExB,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC;YAC7C,IAAI,EAAE,YAAY;YAClB,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC;YACxF,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;YACR,iBAAiB,EAAE,KAAK;SACzB,CAAC,CAAA;IACJ,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,IAAU;QACtC,MAAM,QAAQ,GAAG,MAAM,IAAA,2BAAc,EAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QAEhE,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAEvJ,OAAO,QAAQ,CAAA;IACjB,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,QAAkB;QACtD,MAAM,eAAe,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,CAAA;QACtD,MAAM,mBAAmB,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC,CAAA;QACtF,MAAM,IAAA,qBAAU,EAAC,mBAAmB,EAAE,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC,CAAA;QAC9G,MAAM,IAAA,gBAAK,EAAC,mBAAmB,EAAE,KAAK,CAAC,CAAA;IACzC,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,QAAkB;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAA;QAChC,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,EAAE,GAAG,aAAa,UAAU,CAAC,CAAC,CAAA;QACxG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,EAAE,qBAAqB,EAAE,WAAW,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAA;IAChH,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,QAAkB;QAC9C,MAAM,UAAU,GAAG,MAAM,IAAA,oCAA0B,EAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAA;QACzG,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAA;YAC3F,MAAM,IAAA,uBAAQ,EAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACxC,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,SAAS,CAAC,QAAkB;QACxC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAA;QACrC,MAAM,SAAS,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,EAAC,IAAI,EAAC,EAAE;YACvC,IAAI,IAAI,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC;gBACpB,sDAAsD;gBACtD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;YAC1B,CAAC;YACD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,MAAM,QAAQ,GAAG,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAA;YACjF,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,GAAG,UAAU,EAAE,CAAC,CAAC,CAAA;YAE5H,OAAO,IAAA,uBAAQ,EAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACrC,CAAC,CAAC,CAAA;QAEF,MAAM,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;IAC9B,CAAC;IAEO,wBAAwB,CAAC,SAAiB,EAAE,QAAgB,EAAE,YAAoB,EAAE,IAAU;QACpG,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,CAAA;QAChC,MAAM,EAAE,cAAc,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAA;QACxC,MAAM,WAAW,GAAG,IAAA,gCAAiB,EAAC,IAAI,EAAE,SAAS,CAAC,CAAA;QAEtD,MAAM,QAAQ,GAAoB;YAChC,EAAE,EAAE,aAAa;YACjB,OAAO,EAAE,kBAAkB;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,sBAAsB,CAAC,OAAO;YAC/D,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,cAAc,IAAI,sBAAsB,CAAC,cAAc;YACpF,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,sBAAsB,CAAC,GAAG;YACnD,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,sBAAsB,CAAC,IAAI;YACtD,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,sBAAsB,CAAC,WAAW;YAC3E,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,IAAI,sBAAsB,CAAC,UAAU;YACxE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO;SAC9B,CAAA;QAED,MAAM,YAAY,GAA+B;YAC/C,cAAc,EAAE,OAAO,QAAQ,CAAC,IAAI,IAAI,WAAW,IAAI,QAAQ,CAAC,WAAW,EAAE;YAC7E,iBAAiB,EAAE,WAAW,QAAQ,CAAC,OAAO,IAAI,WAAW,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC1F,aAAa,EAAE,WAAW,QAAQ,CAAC,GAAG,IAAI,WAAW,IAAI,QAAQ,CAAC,cAAc,EAAE;YAClF,IAAI,EAAE,WAAkB;YACxB,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC;YAChD,KAAK,EAAE,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;YACtG,QAAQ,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,aAAa,EAAE,cAAc,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;SACpI,CAAA;QAED,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,CAAA;IACnC,CAAC;CACF;AA1HD,gCA0HC;AAED,MAAM,sBAAsB,GAA4C;IACtE,OAAO,EAAE,0BAA0B;IACnC,cAAc,EAAE,OAAO;IACvB,GAAG,EAAE,qBAAqB;IAC1B,IAAI,EAAE,kCAAkC;IACxC,WAAW,EAAE,OAAO;IACpB,UAAU,EAAE;QACV,wBAAwB;QACxB,kBAAkB;QAClB,cAAc;QACd,aAAa;QACb,UAAU;QACV,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,mCAAmC;QACnC,mBAAmB;QACnB,mCAAmC;QACnC,iBAAiB;QACjB,sCAAsC;QACtC,2CAA2C;KAC5C;CACF,CAAA;AAED,SAAS,wBAAwB,CAAC,cAAsB,EAAE,eAAwB;IAChF,OAAO,eAAe;QACpB,CAAC,CAAC;;;;;qBAKe,cAAc;;qBAEd,cAAc;;CAElC;QACG,CAAC,CAAC;;;;iBAIW,cAAc;CAC9B,CAAA;AACD,CAAC;AAED,SAAS,0BAA0B,CAAC,UAAkB;IACpD,sGAAsG;IACtG,6FAA6F;IAC7F,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAA;AACrE,CAAC", "sourcesContent": ["import { bundle as bundleF<PERSON>pak, FlatpakBundlerBuildOptions, FlatpakManifest } from \"@malept/flatpak-bundler\"\nimport { Arch, copyFile, toLinuxArchString } from \"builder-util\"\nimport { chmod, outputFile } from \"fs-extra\"\nimport * as path from \"path\"\nimport { Target } from \"../core\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { FlatpakOptions } from \"../options/linuxOptions\"\nimport { getNotLocalizedLicenseFile } from \"../util/license\"\nimport { LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { createStageDir, StageDir } from \"./targetUtil\"\n\nexport default class FlatpakTarget extends Target {\n  readonly options: FlatpakOptions = {\n    ...this.packager.platformSpecificBuildOptions,\n    ...(this.packager.config as any)[this.name],\n  }\n\n  constructor(\n    name: string,\n    private readonly packager: LinuxPackager,\n    private helper: LinuxTargetHelper,\n    readonly outDir: string\n  ) {\n    super(name)\n  }\n\n  get appId(): string {\n    return filterFlatpakAppIdentifier(this.packager.appInfo.id)\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const { packager, options } = this\n    const artifactName = packager.expandArtifactNamePattern(options, \"flatpak\", arch, undefined, false)\n    const artifactPath = path.join(this.outDir, artifactName)\n    await packager.info.emitArtifactBuildStarted({\n      targetPresentableName: \"flatpak\",\n      file: artifactPath,\n      arch,\n    })\n\n    const stageDir = await this.prepareStageDir(arch)\n\n    const { manifest, buildOptions } = this.getFlatpakBuilderOptions(appOutDir, stageDir.dir, artifactName, arch)\n    await bundleFlatpak(manifest, buildOptions)\n\n    await stageDir.cleanup()\n\n    await packager.info.emitArtifactBuildCompleted({\n      file: artifactPath,\n      safeArtifactName: packager.computeSafeArtifactName(artifactName, \"flatpak\", arch, false),\n      target: this,\n      arch,\n      packager,\n      isWriteUpdateInfo: false,\n    })\n  }\n\n  private async prepareStageDir(arch: Arch): Promise<StageDir> {\n    const stageDir = await createStageDir(this, this.packager, arch)\n\n    await Promise.all([this.createSandboxBinWrapper(stageDir), this.createDesktopFile(stageDir), this.copyLicenseFile(stageDir), this.copyIcons(stageDir)])\n\n    return stageDir\n  }\n\n  private async createSandboxBinWrapper(stageDir: StageDir) {\n    const useWaylandFlags = !!this.options.useWaylandFlags\n    const electronWrapperPath = stageDir.getTempFile(path.join(\"bin\", \"electron-wrapper\"))\n    await outputFile(electronWrapperPath, getElectronWrapperScript(this.packager.executableName, useWaylandFlags))\n    await chmod(electronWrapperPath, 0o755)\n  }\n\n  private async createDesktopFile(stageDir: StageDir) {\n    const appIdentifier = this.appId\n    const desktopFile = stageDir.getTempFile(path.join(\"share\", \"applications\", `${appIdentifier}.desktop`))\n    await this.helper.writeDesktopEntry(this.options, \"electron-wrapper %U\", desktopFile, { Icon: appIdentifier })\n  }\n\n  private async copyLicenseFile(stageDir: StageDir) {\n    const licenseSrc = await getNotLocalizedLicenseFile(this.options.license, this.packager, [\"txt\", \"html\"])\n    if (licenseSrc) {\n      const licenseDst = stageDir.getTempFile(path.join(\"share\", \"doc\", this.appId, \"copyright\"))\n      await copyFile(licenseSrc, licenseDst)\n    }\n  }\n\n  private async copyIcons(stageDir: StageDir) {\n    const icons = await this.helper.icons\n    const copyIcons = icons.map(async icon => {\n      if (icon.size > 512) {\n        // Flatpak does not allow icons larger than 512 pixels\n        return Promise.resolve()\n      }\n      const extWithDot = path.extname(icon.file)\n      const sizeName = extWithDot === \".svg\" ? \"scalable\" : `${icon.size}x${icon.size}`\n      const iconDst = stageDir.getTempFile(path.join(\"share\", \"icons\", \"hicolor\", sizeName, \"apps\", `${this.appId}${extWithDot}`))\n\n      return copyFile(icon.file, iconDst)\n    })\n\n    await Promise.all(copyIcons)\n  }\n\n  private getFlatpakBuilderOptions(appOutDir: string, stageDir: string, artifactName: string, arch: Arch): { manifest: FlatpakManifest; buildOptions: FlatpakBundlerBuildOptions } {\n    const appIdentifier = this.appId\n    const { executableName } = this.packager\n    const flatpakArch = toLinuxArchString(arch, \"flatpak\")\n\n    const manifest: FlatpakManifest = {\n      id: appIdentifier,\n      command: \"electron-wrapper\",\n      runtime: this.options.runtime || flatpakBuilderDefaults.runtime,\n      runtimeVersion: this.options.runtimeVersion || flatpakBuilderDefaults.runtimeVersion,\n      sdk: this.options.sdk || flatpakBuilderDefaults.sdk,\n      base: this.options.base || flatpakBuilderDefaults.base,\n      baseVersion: this.options.baseVersion || flatpakBuilderDefaults.baseVersion,\n      finishArgs: this.options.finishArgs || flatpakBuilderDefaults.finishArgs,\n      branch: this.options.branch,\n      modules: this.options.modules,\n    }\n\n    const buildOptions: FlatpakBundlerBuildOptions = {\n      baseFlatpakref: `app/${manifest.base}/${flatpakArch}/${manifest.baseVersion}`,\n      runtimeFlatpakref: `runtime/${manifest.runtime}/${flatpakArch}/${manifest.runtimeVersion}`,\n      sdkFlatpakref: `runtime/${manifest.sdk}/${flatpakArch}/${manifest.runtimeVersion}`,\n      arch: flatpakArch as any,\n      bundlePath: path.join(this.outDir, artifactName),\n      files: [[stageDir, \"/\"], [appOutDir, path.join(\"/lib\", appIdentifier)], ...(this.options.files || [])],\n      symlinks: [[path.join(\"/lib\", appIdentifier, executableName), path.join(\"/bin\", executableName)], ...(this.options.symlinks || [])],\n    }\n\n    return { manifest, buildOptions }\n  }\n}\n\nconst flatpakBuilderDefaults: Omit<FlatpakManifest, \"id\" | \"command\"> = {\n  runtime: \"org.freedesktop.Platform\",\n  runtimeVersion: \"20.08\",\n  sdk: \"org.freedesktop.Sdk\",\n  base: \"org.electronjs.Electron2.BaseApp\",\n  baseVersion: \"20.08\",\n  finishArgs: [\n    // Wayland/X11 Rendering\n    \"--socket=wayland\",\n    \"--socket=x11\",\n    \"--share=ipc\",\n    // Open GL\n    \"--device=dri\",\n    // Audio output\n    \"--socket=pulseaudio\",\n    // Read/write home directory access\n    \"--filesystem=home\",\n    // Allow communication with network\n    \"--share=network\",\n    // System notifications with libnotify\n    \"--talk-name=org.freedesktop.Notifications\",\n  ],\n}\n\nfunction getElectronWrapperScript(executableName: string, useWaylandFlags: boolean): string {\n  return useWaylandFlags\n    ? `#!/bin/sh\n\nexport TMPDIR=\"$XDG_RUNTIME_DIR/app/$FLATPAK_ID\"\n\nif [ \"\\${XDG_SESSION_TYPE}\" == \"wayland\" ]; then\n    zypak-wrapper \"${executableName}\" --enable-features=UseOzonePlatform --ozone-platform=wayland \"$@\"\nelse\n    zypak-wrapper \"${executableName}\" \"$@\"\nfi\n`\n    : `#!/bin/sh\n\nexport TMPDIR=\"$XDG_RUNTIME_DIR/app/$FLATPAK_ID\"\n\nzypak-wrapper \"${executableName}\" \"$@\"\n`\n}\n\nfunction filterFlatpakAppIdentifier(identifier: string) {\n  // Remove special characters and allow only alphanumeric (A-Z,a-z,0-9), underscore (_), and period (.)\n  // Flatpak documentation: https://docs.flatpak.org/en/latest/conventions.html#application-ids\n  return identifier.replace(/-/g, \"_\").replace(/[^a-zA-Z0-9._]/g, \"\")\n}\n"]}