{"version": 3, "file": "FpmTarget.js", "sourceRoot": "", "sources": ["../../src/targets/FpmTarget.ts"], "names": [], "mappings": ";;AAAA,+CAAgK;AAEhK,uCAAqD;AACrD,0CAA6C;AAC7C,6BAA4B;AAC5B,wCAAoC;AACpC,kCAAgC;AAChC,kDAAiD;AAIjD,8DAA4E;AAC5E,mDAAiD;AACjD,qDAAgD;AAChD,uCAAuC;AACvC,uDAAoD;AACpD,qDAAqD;AACrD,2DAAsE;AACtE,mCAA2C;AAe3C,MAAqB,SAAU,SAAQ,aAAM;IAK3C,YACE,IAAY,EACK,QAAuB,EACvB,MAAyB,EACjC,MAAc;QAEvB,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;QAJD,aAAQ,GAAR,QAAQ,CAAe;QACvB,WAAM,GAAN,MAAM,CAAmB;QACjC,WAAM,GAAN,MAAM,CAAQ;QARhB,YAAO,GAA+B,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,4BAA4B,EAAE,GAAI,IAAI,CAAC,QAAQ,CAAC,MAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAA;QAY3I,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,CAAA;IACzC,CAAC;IAEO,KAAK,CAAC,aAAa;QACzB,MAAM,mBAAmB,GAAG,IAAA,6BAAe,EAAC,OAAO,CAAC,CAAA;QAEpD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,eAAe,GAAG;YACtB,wBAAwB;YACxB,UAAU,EAAE,QAAQ,CAAC,cAAc;YACnC,oBAAoB,EAAE,QAAQ,CAAC,OAAO,CAAC,oBAAoB;YAC3D,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,eAAe;YACjD,GAAG,QAAQ,CAAC,4BAA4B;SACzC,CAAA;QAED,SAAS,WAAW,CAAC,KAAuB,EAAE,WAAmB;YAC/D,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;gBAClB,OAAO,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAA;YACpD,CAAC;YACD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA;QACjD,CAAC;QAED,OAAO;YACL,YAAY,EAAE,MAAM,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,mBAAmB,CAAC,EAAE,eAAe,CAAC;YAC/I,WAAW,EAAE,MAAM,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,kBAAkB,CAAC,EAAE,eAAe,CAAC;YAC5I,QAAQ,EAAE,MAAM,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE,sBAAsB,CAAC,EAAE,eAAe,CAAC;SAClJ,CAAA;IACH,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,yBAAyB,EAAE,CAAA;IACzC,CAAC;IAEO,KAAK,CAAC,yBAAyB;;QACrC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,OAAO,CAAC,iBAAiB,EAAE,CAAA;QAC7D,MAAM,MAAM,GAAkB,EAAE,CAAA;QAChC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,wFAAwF,CAAC,CAAA;QACvG,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,IAAI,MAAM,GAAG,OAAO,CAAC,UAAU,CAAA;QAC/B,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;YACvC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;gBACjC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,CAAA;YAChD,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,CAAA;YACnC,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAA;QACtC,CAAC;QAED,OAAO;YACL,IAAI,EAAE,MAAA,OAAO,CAAC,WAAW,mCAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB;YACnE,UAAU,EAAE,MAAO;YACnB,GAAG,EAAE,UAAW;YAChB,MAAM,EAAE,OAAO,CAAC,MAAM,IAAI,MAAO;SAClC,CAAA;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,SAAiB,EAAE,IAAU;;QACvC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAA;QAExB,6CAA6C;QAC7C,IAAI,UAAU,GAAG,mCAAmC,CAAA;QACpD,IAAI,cAAc,GAAG,KAAK,CAAA;QAC1B,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,UAAU,GAAG,mCAAmC,CAAA;YAChD,cAAc,GAAG,IAAI,CAAA;QACvB,CAAC;aAAM,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAC5B,UAAU,GAAG,mCAAmC,CAAA;YAChD,cAAc,GAAG,IAAI,CAAA;QACvB,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC9B,MAAM,YAAY,GAAG,QAAQ,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,cAAc,CAAC,CAAA;QAChH,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAEzD,MAAM,QAAQ,CAAC,IAAI,CAAC,wBAAwB,CAAC;YAC3C,qBAAqB,EAAE,MAAM;YAC7B,IAAI,EAAE,YAAY;YAClB,IAAI;SACL,CAAC,CAAA;QAEF,MAAM,IAAA,6BAAc,EAAC,YAAY,CAAC,CAAA;QAClC,IAAI,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACjD,MAAM,IAAA,gBAAK,EAAC,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;QAC/C,CAAC;QACD,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAA,4BAAa,EAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC5H,MAAM,WAAW,GAAG,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,CAAA;QAE3D,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YACnD,CAAC,CAAC,MAAM,IAAA,iDAAgC,EAAC,QAAQ,EAAE,IAAI,EAAE,KAAK,CAAC,oDAAoD,CAAC;YACpH,CAAC,CAAC,IAAI,CAAA;QACR,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,kBAAG,CAAC,IAAI,CAAC,EAAE,WAAW,EAAE,kBAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,EAAE,gCAAgC,MAAM,kBAAkB,CAAC,CAAA;YAC9G,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,gBAAgB,CAAC,EAAE,IAAA,8BAAe,EAAC,aAAa,CAAC,CAAC,CAAA;YAC1F,mEAAmE;YACnE,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,MAAM,CAAC,CAAA;QAClE,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,WAAW,CAAA;QAEtC,0CAA0C;QAC1C,oEAAoE;QACpE,MAAM,IAAA,mBAAQ,EAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAA;QAE5E,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAA;QAChC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC5B,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAA;QACjC,MAAM,IAAI,GAAG;YACX,gBAAgB;YAChB,IAAA,gCAAiB,EAAC,IAAI,EAAE,MAAM,CAAC;YAC/B,iBAAiB;YACjB,OAAO,CAAC,YAAY;YACpB,gBAAgB;YAChB,OAAO,CAAC,WAAW;YACnB,eAAe;YACf,IAAA,iBAAO,EAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;YAC9H,WAAW;YACX,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACvC,WAAW;YACX,YAAY;SACb,CAAA;QAED,IAAA,yBAAY,EAAC,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAQ,CAAC,CAAA;QAEnE,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAA;QAC/C,IAAI,eAAe,IAAI,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,eAAe,CAAC,CAAA;QAC1C,CAAC;QAED,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAC,OAAsB,CAAC,QAAQ,mCAAI,UAAU,CAAC,CAAA;QAC7E,CAAC;aAAM,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YAC5B,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;gBACrB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAA,iBAAO,EAAC,QAAQ,CAAC,CAAC,CAAA;YAC/C,CAAC;QACH,CAAC;QAED,MAAM,gBAAgB,GAAqB;YACzC,IAAI;YACJ,MAAM;SACP,CAAA;QAED,IAAI,OAAO,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAChC,gBAAgB,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACpD,CAAC;QAED,mCAAmC;QACnC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAC/B,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC3B,gBAAgB,CAAC,aAAa,GAAG,OAAO,CAAA;YAC1C,CAAC;iBAAM,CAAC;gBACN,qCAAqC;gBACrC,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,CAAC;oBAChC,gBAAgB,CAAC,aAAa,GAAG,CAAC,OAAiB,CAAC,CAAA;gBACtD,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,CAAC,sDAAsD,OAAO,EAAE,CAAC,CAAA;gBAClF,CAAC;YACH,CAAC;QACH,CAAC;aAAM,CAAC;YACN,gBAAgB,CAAC,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACjE,CAAC;QAED,IAAI,MAAM,KAAK,KAAK,EAAE,CAAC;YACrB,MAAM,UAAU,GAAI,OAAsB,CAAC,UAAU,CAAA;YACrD,IAAI,UAAU,EAAE,CAAC;gBACf,gBAAgB,CAAC,gBAAgB,GAAG,IAAA,sBAAO,EAAC,UAAU,CAAC,CAAA;YACzD,CAAC;iBAAM,CAAC;gBACN,gBAAgB,CAAC,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;YACvE,CAAC;QACH,CAAC;QAED,IAAA,kBAAG,EAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC,CAAA;QACrE,IAAA,kBAAG,EAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAC5B,IAAI,CAAC,IAAI,CACP,aAAa;QACb,kEAAkE;QAClE,kDAAkD;QAClD,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACxB,CACF,CAAA;QAED,IAAA,kBAAG,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAA;QAExC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,KAAK,iCAAa,IAAI,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAA;QAC3E,KAAK,MAAM,IAAI,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1C,MAAM,QAAQ,GAAG,UAAU,KAAK,MAAM,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAA;YACjF,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,6BAA6B,QAAQ,SAAS,QAAQ,CAAC,cAAc,GAAG,UAAU,EAAE,CAAC,CAAA;QAC7G,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAA;QACxD,IAAI,gBAAgB,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,CAAC,IAAI,CAAC,GAAG,gBAAgB,6BAA6B,QAAQ,CAAC,cAAc,MAAM,CAAC,CAAA;QAC1F,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QACzE,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,4BAA4B,QAAQ,CAAC,cAAc,UAAU,CAAC,CAAA;QAE1F,IAAI,QAAQ,CAAC,eAAe,CAAC,uBAAuB,IAAI,IAAI,IAAI,CAAC,MAAM,QAAQ,CAAC,eAAe,CAAC,uBAAuB,CAAC,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC;YAClJ,OAAM;QACR,CAAC;QAED,MAAM,GAAG,GAAG;YACV,GAAG,OAAO,CAAC,GAAG;YACd,QAAQ,EAAE,MAAM,IAAA,yBAAU,GAAE;YAC5B,qBAAqB,EAAE,QAAQ,CAAC,WAAW,KAAK,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;SACpE,CAAA;QAED,6KAA6K;QAC7K,mDAAmD;QACnD,IAAI,MAAM,KAAK,KAAK,IAAI,CAAC,MAAM,IAAA,4BAAa,GAAE,CAAC,EAAE,CAAC;YAChD,MAAM,cAAc,GAAG,MAAM,IAAA,yBAAiB,GAAE,CAAA;YAChD,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE;gBACjB,IAAI,EAAE,IAAA,wBAAU,EAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;gBACtE,iBAAiB,EAAE,IAAA,wBAAU,EAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC,CAAC;aACjG,CAAC,CAAA;QACJ,CAAC;QAED,MAAM,IAAA,gCAAiB,EAAC,CAAC,KAAK,EAAE,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,EAAE,SAAS,EAAE,EAAE,GAAG,EAAE,CAAC,CAAA;QAEzG,IAAI,IAAI,GAAoB;YAC1B,IAAI,EAAE,YAAY;YAClB,MAAM,EAAE,IAAI;YACZ,IAAI;YACJ,QAAQ;SACT,CAAA;QACD,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;YAC1B,IAAI,GAAG;gBACL,GAAG,IAAI;gBACP,gBAAgB,EAAE,QAAQ,CAAC,uBAAuB,CAAC,YAAY,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,cAAc,CAAC;gBAC/F,iBAAiB,EAAE,IAAI;gBACvB,UAAU,EAAE;oBACV,MAAM,EAAE,MAAM,IAAA,eAAQ,EAAC,YAAY,CAAC;oBACpC,IAAI,EAAE,CAAC,MAAM,IAAA,eAAI,EAAC,YAAY,CAAC,CAAC,CAAC,IAAI;iBACtC;aACF,CAAA;QACH,CAAC;QACD,MAAM,QAAQ,CAAC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,CAAA;IACtD,CAAC;IAEO,kBAAkB,CAAC,MAAc;QACvC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IAClD,CAAC;IAEO,iBAAiB,CAAC,MAAc;QACtC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,KAAK;gBACR,OAAO,CAAC,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;YAElI,KAAK,KAAK;gBACR,OAAO;oBACL,MAAM,CAAC,2FAA2F;oBAClG,WAAW;oBACX,KAAK;oBACL,eAAe;oBACf,uBAAuB;oBACvB,WAAW;oBACX,cAAc,CAAC,iBAAiB;oBAChC,uBAAuB,CAAC,iBAAiB;iBAC1C,CAAA;YAEH,KAAK,QAAQ;gBACX,OAAO,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,sBAAsB,CAAC,CAAA;YAEvK;gBACE,OAAO,EAAE,CAAA;QACb,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,MAAc;QACzC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,KAAK;gBACR,OAAO,CAAC,oBAAoB,CAAC,CAAA;YAC/B;gBACE,OAAO,EAAE,CAAA;QACb,CAAC;IACH,CAAC;CACF;AA1SD,4BA0SC;AAUD,KAAK,UAAU,eAAe,CAAC,MAAc,EAAE,YAAoB,EAAE,OAAY;IAC/E,mCAAmC;IACnC,SAAS,QAAQ,CAAC,KAAa,EAAE,EAAU;QACzC,IAAI,EAAE,IAAI,OAAO,EAAE,CAAC;YAClB,OAAO,OAAO,CAAC,EAAE,CAAC,CAAA;QACpB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI,KAAK,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAA;QAC/C,CAAC;IACH,CAAC;IACD,MAAM,MAAM,GAAG,CAAC,MAAM,IAAA,mBAAQ,EAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE;QACrI,kBAAG,CAAC,IAAI,CAAC,6DAA6D,CAAC,CAAA;QACvE,OAAO,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;IACnC,CAAC,CAAC,CAAA;IAEF,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,EAAE,CAAC,CAAA;IAC5F,MAAM,IAAA,qBAAU,EAAC,UAAU,EAAE,MAAM,CAAC,CAAA;IACpC,OAAO,UAAU,CAAA;AACnB,CAAC", "sourcesContent": ["import { <PERSON>, as<PERSON><PERSON>y, executeApp<PERSON>uilder, getArchSuffix, getPath7za, log, serializeToYaml, TmpDir, toLinuxArchString, unlinkIfExists, use } from \"builder-util\"\nimport { Nullish } from \"builder-util-runtime\"\nimport { copyFile, outputFile, stat } from \"fs-extra\"\nimport { mkdir, readFile } from \"fs/promises\"\nimport * as path from \"path\"\nimport { smarten } from \"../appInfo\"\nimport { Target } from \"../core\"\nimport * as errorMessages from \"../errorMessages\"\nimport { LinuxPackager } from \"../linuxPackager\"\nimport { DebOptions, LinuxTargetSpecificOptions } from \"../options/linuxOptions\"\nimport { ArtifactCreated } from \"../packagerApi\"\nimport { getAppUpdatePublishConfiguration } from \"../publish/PublishManager\"\nimport { objectToArgs } from \"../util/appBuilder\"\nimport { computeEnv } from \"../util/bundledTool\"\nimport { hashFile } from \"../util/hash\"\nimport { isMacOsSierra } from \"../util/macosVersion\"\nimport { getTemplatePath } from \"../util/pathManager\"\nimport { installPrefix, LinuxTargetHelper } from \"./LinuxTargetHelper\"\nimport { getLinuxToolsPath } from \"./tools\"\n\ninterface FpmOptions {\n  name: string\n  maintainer: string | undefined\n  vendor: string\n  url: string\n}\n\ninterface ScriptFiles {\n  afterRemove: string\n  afterInstall: string\n  appArmor: string\n}\n\nexport default class FpmTarget extends Target {\n  readonly options: LinuxTargetSpecificOptions = { ...this.packager.platformSpecificBuildOptions, ...(this.packager.config as any)[this.name] }\n\n  private readonly scriptFiles: Promise<ScriptFiles>\n\n  constructor(\n    name: string,\n    private readonly packager: LinuxPackager,\n    private readonly helper: LinuxTargetHelper,\n    readonly outDir: string\n  ) {\n    super(name, false)\n\n    this.scriptFiles = this.createScripts()\n  }\n\n  private async createScripts(): Promise<ScriptFiles> {\n    const defaultTemplatesDir = getTemplatePath(\"linux\")\n\n    const packager = this.packager\n    const templateOptions = {\n      // old API compatibility\n      executable: packager.executableName,\n      sanitizedProductName: packager.appInfo.sanitizedProductName,\n      productFilename: packager.appInfo.productFilename,\n      ...packager.platformSpecificBuildOptions,\n    }\n\n    function getResource(value: string | Nullish, defaultFile: string) {\n      if (value == null) {\n        return path.join(defaultTemplatesDir, defaultFile)\n      }\n      return path.resolve(packager.projectDir, value)\n    }\n\n    return {\n      afterInstall: await writeConfigFile(packager.info.tempDirManager, getResource(this.options.afterInstall, \"after-install.tpl\"), templateOptions),\n      afterRemove: await writeConfigFile(packager.info.tempDirManager, getResource(this.options.afterRemove, \"after-remove.tpl\"), templateOptions),\n      appArmor: await writeConfigFile(packager.info.tempDirManager, getResource(this.options.appArmorProfile, \"apparmor-profile.tpl\"), templateOptions),\n    }\n  }\n\n  checkOptions(): Promise<any> {\n    return this.computeFpmMetaInfoOptions()\n  }\n\n  private async computeFpmMetaInfoOptions(): Promise<FpmOptions> {\n    const packager = this.packager\n    const projectUrl = await packager.appInfo.computePackageUrl()\n    const errors: Array<string> = []\n    if (projectUrl == null) {\n      errors.push(\"Please specify project homepage, see https://www.electron.build/configuration#metadata\")\n    }\n\n    const options = this.options\n    let author = options.maintainer\n    if (author == null) {\n      const a = packager.info.metadata.author\n      if (a == null || a.email == null) {\n        errors.push(errorMessages.authorEmailIsMissed)\n      } else {\n        author = `${a.name} <${a.email}>`\n      }\n    }\n\n    if (errors.length > 0) {\n      throw new Error(errors.join(\"\\n\\n\"))\n    }\n\n    return {\n      name: options.packageName ?? this.packager.appInfo.linuxPackageName,\n      maintainer: author!,\n      url: projectUrl!,\n      vendor: options.vendor || author!,\n    }\n  }\n\n  async build(appOutDir: string, arch: Arch): Promise<any> {\n    const target = this.name\n\n    // tslint:disable:no-invalid-template-strings\n    let nameFormat = \"${name}-${version}-${arch}.${ext}\"\n    let isUseArchIfX64 = false\n    if (target === \"deb\") {\n      nameFormat = \"${name}_${version}_${arch}.${ext}\"\n      isUseArchIfX64 = true\n    } else if (target === \"rpm\") {\n      nameFormat = \"${name}-${version}.${arch}.${ext}\"\n      isUseArchIfX64 = true\n    }\n\n    const packager = this.packager\n    const artifactName = packager.expandArtifactNamePattern(this.options, target, arch, nameFormat, !isUseArchIfX64)\n    const artifactPath = path.join(this.outDir, artifactName)\n\n    await packager.info.emitArtifactBuildStarted({\n      targetPresentableName: target,\n      file: artifactPath,\n      arch,\n    })\n\n    await unlinkIfExists(artifactPath)\n    if (packager.packagerOptions.prepackaged != null) {\n      await mkdir(this.outDir, { recursive: true })\n    }\n    const linuxDistType = packager.packagerOptions.prepackaged || path.join(this.outDir, `linux${getArchSuffix(arch)}-unpacked`)\n    const resourceDir = packager.getResourcesDir(linuxDistType)\n\n    const publishConfig = this.supportsAutoUpdate(target)\n      ? await getAppUpdatePublishConfiguration(packager, arch, false /* in any case validation will be done on publish */)\n      : null\n    if (publishConfig != null) {\n      log.info({ resourceDir: log.filePath(resourceDir) }, `adding autoupdate files for: ${target}. (Beta feature)`)\n      await outputFile(path.join(resourceDir, \"app-update.yml\"), serializeToYaml(publishConfig))\n      // Extra file needed for auto-updater to detect installation method\n      await outputFile(path.join(resourceDir, \"package-type\"), target)\n    }\n\n    const scripts = await this.scriptFiles\n\n    // Install AppArmor support for ubuntu 24+\n    // https://github.com/electron-userland/electron-builder/issues/8635\n    await copyFile(scripts.appArmor, path.join(resourceDir, \"apparmor-profile\"))\n\n    const appInfo = packager.appInfo\n    const options = this.options\n    const synopsis = options.synopsis\n    const args = [\n      \"--architecture\",\n      toLinuxArchString(arch, target),\n      \"--after-install\",\n      scripts.afterInstall,\n      \"--after-remove\",\n      scripts.afterRemove,\n      \"--description\",\n      smarten(target === \"rpm\" ? this.helper.getDescription(options) : `${synopsis || \"\"}\\n ${this.helper.getDescription(options)}`),\n      \"--version\",\n      this.helper.getSanitizedVersion(target),\n      \"--package\",\n      artifactPath,\n    ]\n\n    objectToArgs(args, (await this.computeFpmMetaInfoOptions()) as any)\n\n    const packageCategory = options.packageCategory\n    if (packageCategory != null) {\n      args.push(\"--category\", packageCategory)\n    }\n\n    if (target === \"deb\") {\n      args.push(\"--deb-priority\", (options as DebOptions).priority ?? \"optional\")\n    } else if (target === \"rpm\") {\n      if (synopsis != null) {\n        args.push(\"--rpm-summary\", smarten(synopsis))\n      }\n    }\n\n    const fpmConfiguration: FpmConfiguration = {\n      args,\n      target,\n    }\n\n    if (options.compression != null) {\n      fpmConfiguration.compression = options.compression\n    }\n\n    // noinspection JSDeprecatedSymbols\n    const depends = options.depends\n    if (depends != null) {\n      if (Array.isArray(depends)) {\n        fpmConfiguration.customDepends = depends\n      } else {\n        // noinspection SuspiciousTypeOfGuard\n        if (typeof depends === \"string\") {\n          fpmConfiguration.customDepends = [depends as string]\n        } else {\n          throw new Error(`depends must be Array or String, but specified as: ${depends}`)\n        }\n      }\n    } else {\n      fpmConfiguration.customDepends = this.getDefaultDepends(target)\n    }\n\n    if (target === \"deb\") {\n      const recommends = (options as DebOptions).recommends\n      if (recommends) {\n        fpmConfiguration.customRecommends = asArray(recommends)\n      } else {\n        fpmConfiguration.customRecommends = this.getDefaultRecommends(target)\n      }\n    }\n\n    use(packager.info.metadata.license, it => args.push(\"--license\", it))\n    use(appInfo.buildNumber, it =>\n      args.push(\n        \"--iteration\",\n        // dashes are not supported for iteration in older versions of fpm\n        // https://github.com/jordansissel/fpm/issues/1833\n        it.split(\"-\").join(\"_\")\n      )\n    )\n\n    use(options.fpm, it => args.push(...it))\n\n    args.push(`${appOutDir}/=${installPrefix}/${appInfo.sanitizedProductName}`)\n    for (const icon of await this.helper.icons) {\n      const extWithDot = path.extname(icon.file)\n      const sizeName = extWithDot === \".svg\" ? \"scalable\" : `${icon.size}x${icon.size}`\n      args.push(`${icon.file}=/usr/share/icons/hicolor/${sizeName}/apps/${packager.executableName}${extWithDot}`)\n    }\n\n    const mimeTypeFilePath = await this.helper.mimeTypeFiles\n    if (mimeTypeFilePath != null) {\n      args.push(`${mimeTypeFilePath}=/usr/share/mime/packages/${packager.executableName}.xml`)\n    }\n\n    const desktopFilePath = await this.helper.writeDesktopEntry(this.options)\n    args.push(`${desktopFilePath}=/usr/share/applications/${packager.executableName}.desktop`)\n\n    if (packager.packagerOptions.effectiveOptionComputed != null && (await packager.packagerOptions.effectiveOptionComputed([args, desktopFilePath]))) {\n      return\n    }\n\n    const env = {\n      ...process.env,\n      SZA_PATH: await getPath7za(),\n      SZA_COMPRESSION_LEVEL: packager.compression === \"store\" ? \"0\" : \"9\",\n    }\n\n    // rpmbuild wants directory rpm with some default config files. Even if we can use dylibbundler, path to such config files are not changed (we need to replace in the binary)\n    // so, for now, brew install rpm is still required.\n    if (target !== \"rpm\" && (await isMacOsSierra())) {\n      const linuxToolsPath = await getLinuxToolsPath()\n      Object.assign(env, {\n        PATH: computeEnv(process.env.PATH, [path.join(linuxToolsPath, \"bin\")]),\n        DYLD_LIBRARY_PATH: computeEnv(process.env.DYLD_LIBRARY_PATH, [path.join(linuxToolsPath, \"lib\")]),\n      })\n    }\n\n    await executeAppBuilder([\"fpm\", \"--configuration\", JSON.stringify(fpmConfiguration)], undefined, { env })\n\n    let info: ArtifactCreated = {\n      file: artifactPath,\n      target: this,\n      arch,\n      packager,\n    }\n    if (publishConfig != null) {\n      info = {\n        ...info,\n        safeArtifactName: packager.computeSafeArtifactName(artifactName, target, arch, !isUseArchIfX64),\n        isWriteUpdateInfo: true,\n        updateInfo: {\n          sha512: await hashFile(artifactPath),\n          size: (await stat(artifactPath)).size,\n        },\n      }\n    }\n    await packager.info.emitArtifactBuildCompleted(info)\n  }\n\n  private supportsAutoUpdate(target: string) {\n    return [\"deb\", \"rpm\", \"pacman\"].includes(target)\n  }\n\n  private getDefaultDepends(target: string): string[] {\n    switch (target) {\n      case \"deb\":\n        return [\"libgtk-3-0\", \"libnotify4\", \"libnss3\", \"libxss1\", \"libxtst6\", \"xdg-utils\", \"libatspi2.0-0\", \"libuuid1\", \"libsecret-1-0\"]\n\n      case \"rpm\":\n        return [\n          \"gtk3\" /* for electron 2+ (electron 1 uses gtk2, but this old version is not supported anymore) */,\n          \"libnotify\",\n          \"nss\",\n          \"libXScrnSaver\",\n          \"(libXtst or libXtst6)\",\n          \"xdg-utils\",\n          \"at-spi2-core\" /* since 5.0.0 */,\n          \"(libuuid or libuuid1)\" /* since 4.0.0 */,\n        ]\n\n      case \"pacman\":\n        return [\"c-ares\", \"ffmpeg\", \"gtk3\", \"http-parser\", \"libevent\", \"libvpx\", \"libxslt\", \"libxss\", \"minizip\", \"nss\", \"re2\", \"snappy\", \"libnotify\", \"libappindicator-gtk3\"]\n\n      default:\n        return []\n    }\n  }\n\n  private getDefaultRecommends(target: string): string[] {\n    switch (target) {\n      case \"deb\":\n        return [\"libappindicator3-1\"]\n      default:\n        return []\n    }\n  }\n}\n\ninterface FpmConfiguration {\n  target: string\n  args: Array<string>\n  customDepends?: Array<string>\n  customRecommends?: Array<string>\n  compression?: string | null\n}\n\nasync function writeConfigFile(tmpDir: TmpDir, templatePath: string, options: any): Promise<string> {\n  //noinspection JSUnusedLocalSymbols\n  function replacer(match: string, p1: string) {\n    if (p1 in options) {\n      return options[p1]\n    } else {\n      throw new Error(`Macro ${p1} is not defined`)\n    }\n  }\n  const config = (await readFile(templatePath, \"utf8\")).replace(/\\${([a-zA-Z]+)}/g, replacer).replace(/<%=([a-zA-Z]+)%>/g, (match, p1) => {\n    log.warn(\"<%= varName %> is deprecated, please use ${varName} instead\")\n    return replacer(match, p1.trim())\n  })\n\n  const outputPath = await tmpDir.getTempFile({ suffix: path.basename(templatePath, \".tpl\") })\n  await outputFile(outputPath, config)\n  return outputPath\n}\n"]}