{"version": 3, "file": "core.js", "sourceRoot": "", "sources": ["../src/core.ts"], "names": [], "mappings": ";;;AAAA,+CAA6D;AAoB7D,MAAa,QAAQ;IAKnB,YACS,IAAY,EACZ,qBAA6B,EAC7B,QAAyB;QAFzB,SAAI,GAAJ,IAAI,CAAQ;QACZ,0BAAqB,GAArB,qBAAqB,CAAQ;QAC7B,aAAQ,GAAR,QAAQ,CAAiB;IAC/B,CAAC;IAEJ,QAAQ;QACN,OAAO,IAAI,CAAC,IAAI,CAAA;IAClB,CAAC;IAED,YAAY,CAAC,IAAoC,EAAE,GAAG,KAAkB;QACtE,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1D,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;QACrC,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,GAAG,EAAE,CAAA;QAE5B,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAA,6BAAc,EAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;YAChG,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC/E,CAAC;QACD,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAA;IACtC,CAAC;IAED,MAAM,CAAC,OAAO;QACZ,OAAO,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC9C,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,IAAY;QAC5B,IAAI,GAAG,IAAI,CAAC,WAAW,EAAE,CAAA;QACzB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC;YAC3B,KAAK,QAAQ,CAAC,GAAG,CAAC,IAAI;gBACpB,OAAO,QAAQ,CAAC,GAAG,CAAA;YAErB,KAAK,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC;YAC/B,KAAK,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;YAC3B,KAAK,QAAQ,CAAC,OAAO,CAAC,qBAAqB;gBACzC,OAAO,QAAQ,CAAC,OAAO,CAAA;YAEzB,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ;gBAC1B,OAAO,QAAQ,CAAC,KAAK,CAAA;YAEvB;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAA;QAChD,CAAC;IACH,CAAC;;AAlDH,4BAmDC;AAlDQ,YAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAA;AAC1C,cAAK,GAAG,IAAI,QAAQ,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAA;AAC/C,gBAAO,GAAG,IAAI,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,OAAO,CAAC,CAAA;AAkD1D,MAAsB,MAAM;IAI1B,YACW,IAAY,EACZ,mBAA4B,IAAI;QADhC,SAAI,GAAJ,IAAI,CAAQ;QACZ,qBAAgB,GAAhB,gBAAgB,CAAgB;IACxC,CAAC;IAEJ,KAAK,CAAC,YAAY;QAChB,SAAS;IACX,CAAC;IAID,WAAW;QACT,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;IAC1B,CAAC;CACF;AAlBD,wBAkBC;AAWY,QAAA,cAAc,GAAG,SAAS,CAAA;AAC1B,QAAA,UAAU,GAAG,KAAK,CAAA", "sourcesContent": ["import { Arch, archFromString, ArchType } from \"builder-util\"\nimport { AllPublishOptions, Nullish } from \"builder-util-runtime\"\n\n// https://github.com/YousefED/typescript-json-schema/issues/80\nexport type Publish = AllPublishOptions | Array<AllPublishOptions> | null\n\nexport type TargetConfigType = Array<string | TargetConfiguration> | string | TargetConfiguration | null\n\nexport interface TargetConfiguration {\n  /**\n   * The target name. e.g. `snap`.\n   */\n  readonly target: string\n\n  /**\n   * The arch or list of archs.\n   */\n  readonly arch?: Array<ArchType> | ArchType\n}\n\nexport class Platform {\n  static MAC = new Platform(\"mac\", \"mac\", \"darwin\")\n  static LINUX = new Platform(\"linux\", \"linux\", \"linux\")\n  static WINDOWS = new Platform(\"windows\", \"win\", \"win32\")\n\n  constructor(\n    public name: string,\n    public buildConfigurationKey: string,\n    public nodeName: NodeJS.Platform\n  ) {}\n\n  toString() {\n    return this.name\n  }\n\n  createTarget(type?: string | Array<string> | null, ...archs: Array<Arch>): Map<Platform, Map<Arch, Array<string>>> {\n    if (type == null && (archs == null || archs.length === 0)) {\n      return new Map([[this, new Map()]])\n    }\n\n    const archToType = new Map()\n\n    for (const arch of archs == null || archs.length === 0 ? [archFromString(process.arch)] : archs) {\n      archToType.set(arch, type == null ? [] : Array.isArray(type) ? type : [type])\n    }\n    return new Map([[this, archToType]])\n  }\n\n  static current(): Platform {\n    return Platform.fromString(process.platform)\n  }\n\n  static fromString(name: string): Platform {\n    name = name.toLowerCase()\n    switch (name) {\n      case Platform.MAC.nodeName:\n      case Platform.MAC.name:\n        return Platform.MAC\n\n      case Platform.WINDOWS.nodeName:\n      case Platform.WINDOWS.name:\n      case Platform.WINDOWS.buildConfigurationKey:\n        return Platform.WINDOWS\n\n      case Platform.LINUX.nodeName:\n        return Platform.LINUX\n\n      default:\n        throw new Error(`Unknown platform: ${name}`)\n    }\n  }\n}\n\nexport abstract class Target {\n  abstract readonly outDir: string\n  abstract readonly options: TargetSpecificOptions | Nullish\n\n  protected constructor(\n    readonly name: string,\n    readonly isAsyncSupported: boolean = true\n  ) {}\n\n  async checkOptions(): Promise<any> {\n    // ignore\n  }\n\n  abstract build(appOutDir: string, arch: Arch): Promise<any>\n\n  finishBuild(): Promise<any> {\n    return Promise.resolve()\n  }\n}\n\nexport interface TargetSpecificOptions {\n  /**\n   The [artifact file name template](./configuration.md#artifact-file-name-template).\n   */\n  readonly artifactName?: string | null\n\n  publish?: Publish\n}\n\nexport const DEFAULT_TARGET = \"default\"\nexport const DIR_TARGET = \"dir\"\n\nexport type CompressionLevel = \"store\" | \"normal\" | \"maximum\"\n\nexport interface BeforeBuildContext {\n  readonly appDir: string\n  readonly electronVersion: string\n  readonly platform: Platform\n  readonly arch: string\n}\n\nexport interface SourceRepositoryInfo {\n  type?: string\n  domain?: string\n  user: string\n  project: string\n}\n"]}