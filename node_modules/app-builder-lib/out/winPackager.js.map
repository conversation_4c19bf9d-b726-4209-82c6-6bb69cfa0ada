{"version": 3, "file": "winPackager.js", "sourceRoot": "", "sources": ["../src/winPackager.ts"], "names": [], "mappings": ";;;AAAA,+CAAkI;AAElI,mCAAmC;AACnC,0CAAqC;AACrC,8BAA6B;AAC7B,uCAA+B;AAC/B,6BAA4B;AAE5B,gEAA4E;AAC5E,gFAA4E;AAC5E,8EAAkH;AAElH,iCAAqD;AAGrD,yDAAoE;AAIpE,0DAAsD;AACtD,sDAA6E;AAC7E,0EAAsE;AACtE,2DAA4D;AAC5D,sDAA+D;AAC/D,wCAAkD;AAClD,wCAAmC;AACnC,gCAAiD;AACjD,iCAAiC;AAEjC,MAAa,WAAY,SAAQ,mCAAsC;IAgBrE,IAAI,8BAA8B;QAChC,OAAO,IAAI,CAAC,4BAA4B,CAAC,yBAAyB,KAAK,KAAK,CAAA;IAC9E,CAAC;IAED,YAAY,IAAc;QACxB,KAAK,CAAC,IAAI,EAAE,eAAQ,CAAC,OAAO,CAAC,CAAA;QApB/B,cAAS,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAA;QAE/C,OAAE,GAAG,IAAI,eAAI,CAAY,GAAG,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,cAAS,EAAE,CAAC,CAAC,CAAC,CAAC,IAAA,iBAAY,EAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QAElI,mBAAc,GAAG,IAAI,eAAI,CAAC,KAAK,IAAI,EAAE;YAC5C,IAAI,OAAoB,CAAA;YACxB,IAAI,IAAI,CAAC,4BAA4B,CAAC,gBAAgB,IAAI,IAAI,EAAE,CAAC;gBAC/D,OAAO,GAAG,IAAI,iDAAuB,CAAC,IAAI,CAAC,CAAA;YAC7C,CAAC;iBAAM,CAAC;gBACN,OAAO,GAAG,IAAI,+CAAsB,CAAC,IAAI,CAAC,CAAA;YAC5C,CAAC;YACD,MAAM,OAAO,CAAC,UAAU,EAAE,CAAA;YAC1B,OAAO,OAAO,CAAA;QAChB,CAAC,CAAC,CAAA;IAQF,CAAC;IAED,IAAI,aAAa;QACf,OAAO,CAAC,MAAM,CAAC,CAAA;IACjB,CAAC;IAED,aAAa,CAAC,OAAsB,EAAE,MAAmE;QACvG,IAAI,iBAA2C,CAAA;QAC/C,MAAM,oBAAoB,GAAG,GAAG,EAAE;YAChC,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;gBAC9B,iBAAiB,GAAG,IAAI,4BAAiB,EAAE,CAAA;YAC7C,CAAC;YACD,OAAO,iBAAiB,CAAA;QAC1B,CAAC,CAAA;QAED,IAAI,MAA+B,CAAA;QACnC,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,IAAI,MAAM,IAAI,IAAI,EAAE,CAAC;gBACnB,MAAM,GAAG,IAAI,2BAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAA;YACvD,CAAC;YACD,OAAO,MAAM,CAAA;QACf,CAAC,CAAA;QAED,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;YAC3B,IAAI,IAAI,KAAK,iBAAU,EAAE,CAAC;gBACxB,SAAQ;YACV,CAAC;YAED,IAAI,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC3C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,uBAAU,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;YACzE,CAAC;iBAAM,IAAI,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC/B,+CAA+C;gBAC/C,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,IAAI,uCAAkB,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,2BAAgB,CAAC,oBAAoB,EAAE,CAAC,CAAC,CAAC,CAAA;YACnI,CAAC;iBAAM,CAAC;gBACN,MAAM,WAAW,GAA8F,CAAC,GAAG,EAAE;oBACnH,QAAQ,IAAI,EAAE,CAAC;wBACb,KAAK,UAAU;4BACb,IAAI,CAAC;gCACH,OAAO,OAAO,CAAC,mCAAmC,CAAC,CAAC,OAAO,CAAA;4BAC7D,CAAC;4BAAC,OAAO,CAAM,EAAE,CAAC;gCAChB,MAAM,IAAI,wCAAyB,CAAC,qGAAqG,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAA;4BAC1J,CAAC;wBAEH,KAAK,MAAM;4BACT,OAAO,OAAO,CAAC,sBAAsB,CAAC,CAAC,OAAO,CAAA;wBAEhD,KAAK,KAAK;4BACR,OAAO,OAAO,CAAC,qBAAqB,CAAC,CAAC,OAAO,CAAA;wBAE/C,KAAK,YAAY;4BACf,OAAO,OAAO,CAAC,4BAA4B,CAAC,CAAC,OAAO,CAAA;wBAEtD;4BACE,OAAO,IAAI,CAAA;oBACf,CAAC;gBACH,CAAC,CAAC,EAAE,CAAA;gBAEJ,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,KAAK,IAAI,CAAC,CAAC,CAAC,IAAA,kCAAkB,EAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,IAAK,WAAmB,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC,CAAA;YACxI,CAAC;QACH,CAAC;IACH,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAA;IAC7B,CAAC;IAED,gBAAgB;;QACd,OAAO,IAAA,gCAAa,EAAC,IAAA,gCAAa,EAAC,MAAA,IAAI,CAAC,4BAA4B,CAAC,eAAe,0CAAE,mBAAmB,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAA;IACzK,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,IAAY;QACrB,MAAM,WAAW,GAAuB;YACtC,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,IAAI,CAAC,4BAA4B;SAC3C,CAAA;QAED,MAAM,mBAAmB,GAAG,MAAM,IAAA,6BAAW,EAAC,WAAW,EAAE,IAAI,CAAC,CAAA;QAChE,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAClD,MAAM,IAAI,wCAAyB,CACjC,mKAAmK,CACpK,CAAA;QACH,CAAC;QACD,OAAO,mBAAmB,CAAA;IAC5B,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,IAAY,EAAE,IAAU,EAAE,MAAc,EAAE,YAA4B,EAAE,uBAAwD;;QACzJ,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAE5B,MAAM,KAAK,GAAkB,EAAE,CAAA;QAE/B,MAAM,IAAI,GAAG;YACX,IAAI;YACJ,sBAAsB;YACtB,iBAAiB;YACjB,OAAO,CAAC,WAAW;YACnB,sBAAsB;YACtB,aAAa;YACb,OAAO,CAAC,WAAW;YACnB,sBAAsB;YACtB,gBAAgB;YAChB,OAAO,CAAC,SAAS;YACjB,oBAAoB;YACpB,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY;YAC5C,uBAAuB;YACvB,OAAO,CAAC,mBAAmB,IAAI,OAAO,CAAC,4BAA4B,EAAE;SACtE,CAAA;QAED,IAAI,YAAY,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,cAAc,EAAE,YAAY,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,EAAE,CAAC,CAAA;QACjH,CAAC;QAED,IAAI,uBAAuB,IAAI,IAAI,IAAI,uBAAuB,KAAK,WAAW,EAAE,CAAC;YAC/E,IAAI,CAAC,IAAI,CAAC,iCAAiC,EAAE,uBAAuB,CAAC,CAAA;QACvE,CAAC;QAED,IAAA,kBAAG,EAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC,CAAA;QACpF,IAAA,kBAAG,EAAC,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,EAAE,CAAC,CAAC,CAAA;QACtH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAA;QACzC,IAAA,kBAAG,EAAC,QAAQ,EAAE,EAAE,CAAC,EAAE;YACjB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACd,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;QAC7B,CAAC,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;QAC1B,MAAM,qBAAqB,GAAG,CAAC,IAAA,2BAAmB,GAAE,IAAI,IAAI,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,CAAA;QAC1J,IAAI,iBAAiB,GAA6B,IAAI,CAAA;QACtD,8GAA8G;QAC9G,IAAI,qBAAqB,IAAI,IAAI,EAAE,CAAC;YAClC,MAAM,OAAO,GAAI,qBAA6C,CAAC,IAAI,CAAA;YACnE,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;gBACpB,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACrB,CAAC;YAED,MAAM,KAAK,GAAG,IAAA,YAAI,EAAC,kBAAkB,CAAC,CAAA;YACtC,MAAM,IAAI,GAAG,IAAA,mBAAU,EAAC,QAAQ,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,eAAe,IAAI,oBAAoB,CAAC,CAAA;YAC3D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC,CAAA;YAC9D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;YACjC,IAAI,CAAC,MAAM,CAAC,CAAA,MAAA,IAAI,CAAC,4BAA4B,CAAC,eAAe,0CAAE,eAAe,KAAI,oBAAoB,CAAC,CAAA;YACvG,IAAI,CAAC,MAAM,CAAC,CAAA,MAAA,IAAI,CAAC,4BAA4B,CAAC,eAAe,0CAAE,sBAAsB,KAAI,gBAAgB,CAAC,CAAA;YAE1G,iBAAiB,GAAG,IAAI,gCAAiB,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;YAC7D,IAAI,MAAM,iBAAiB,CAAC,WAAW,CAAC,MAAM,IAAA,qBAAM,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC;gBACnE,KAAK,CAAC,GAAG,EAAE,CAAA;gBACX,OAAM;YACR,CAAC;YACD,KAAK,CAAC,GAAG,EAAE,CAAA;QACb,CAAC;QAED,MAAM,KAAK,GAAG,IAAA,YAAI,EAAC,WAAW,CAAC,CAAA;QAC/B,8DAA8D;QAC9D,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAClE,MAAM,IAAA,gCAAiB,EAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,SAAS,CAAC,mBAAmB,EAAE,EAAE,EAAE,CAAC,CAAC,uBAAuB,CAAC,CAAA;QACnI,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;YACnD,MAAM,UAAU,GAAG,MAAM,IAAA,0CAAiB,GAAE,CAAA;YAC5C,MAAM,IAAA,eAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,IAAI,CAAC,CAAA;QACzG,CAAC;QAED,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACrB,KAAK,CAAC,GAAG,EAAE,CAAA;QAEX,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;YAC9B,MAAM,iBAAiB,CAAC,IAAI,EAAE,CAAA;QAChC,CAAC;IACH,CAAC;IAEO,cAAc,CAAC,IAAY;;QACjC,MAAM,kBAAkB,GAAG,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,4BAA4B,CAAC,QAAQ,0CAAE,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAA,CAAA;QACxG,OAAO,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;IACpD,CAAC;IAES,8BAA8B,CAAC,WAA6B;QACpE,IAAI,IAAI,CAAC,4BAA4B,CAAC,qBAAqB,KAAK,KAAK,EAAE,CAAC;YACtE,OAAO,IAAI,CAAA;QACb,CAAC;QAED,OAAO,IAAI,CAAC,EAAE;YACZ,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;gBACpC,IAAI,SAAS,KAAK,WAAW,CAAC,SAAS,EAAE,CAAC;oBACxC,OAAO,IAAI,kCAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBACzD,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAA;QACb,CAAC,CAAA;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CAAC,WAA6B,EAAE,MAAe;QACpE,MAAM,WAAW,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,eAAe,MAAM,CAAA;QACzD,IAAI,IAAI,CAAC,4BAA4B,CAAC,qBAAqB,KAAK,KAAK,EAAE,CAAC;YACtE,OAAO,KAAK,CAAA;QACd,CAAC;QAED,MAAM,KAAK,GAAG,MAAM,IAAA,kBAAO,EAAC,WAAW,CAAC,SAAS,CAAC,CAAA;QAClD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,KAAK,WAAW,EAAE,CAAC;gBACzB,MAAM,IAAI,CAAC,oBAAoB,CAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,EAC7C,WAAW,CAAC,IAAI,EAChB,WAAW,CAAC,MAAM,EAClB,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,EAClC,IAAI,CAAC,4BAA4B,CAAC,uBAAuB,CAC1D,CAAA;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC,CAAA;YACzD,CAAC;QACH,CAAC;QAED,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,IAAI,CAAA;QACb,CAAC;QAED,MAAM,YAAY,GAAG,CAAC,QAAkB,EAAE,EAAE;YAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,GAAG,QAAQ,CAAC,CAAA;YAC5D,OAAO,IAAA,mBAAI,EAAC,MAAM,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAA;QACtF,CAAC,CAAA;QACD,MAAM,WAAW,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAA;QACxH,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;YACvC,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QACvB,CAAC;QAED,OAAO,IAAI,CAAA;IACb,CAAC;CACF;AArPD,kCAqPC", "sourcesContent": ["import { Arch, CopyFileTransformer, execute<PERSON><PERSON><PERSON><PERSON>er, FileTransformer, InvalidConfigurationError, use, walk } from \"builder-util\"\nimport { Nullish } from \"builder-util-runtime\"\nimport { createHash } from \"crypto\"\nimport { readdir } from \"fs/promises\"\nimport * as isCI from \"is-ci\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { SignManager } from \"./codeSign/signManager\"\nimport { signWindows, WindowsSignOptions } from \"./codeSign/windowsCodeSign\"\nimport { WindowsSignAzureManager } from \"./codeSign/windowsSignAzureManager\"\nimport { FileCodeSigningInfo, getSignVendorPath, WindowsSignToolManager } from \"./codeSign/windowsSignToolManager\"\nimport { AfterPackContext } from \"./configuration\"\nimport { DIR_TARGET, Platform, Target } from \"./core\"\nimport { RequestedExecutionLevel, WindowsConfiguration } from \"./options/winOptions\"\nimport { Packager } from \"./packager\"\nimport { chooseNotNull, PlatformPackager } from \"./platformPackager\"\nimport AppXTarget from \"./targets/AppxTarget\"\nimport MsiTarget from \"./targets/MsiTarget\"\nimport MsiWrappedTarget from \"./targets/MsiWrappedTarget\"\nimport { NsisTarget } from \"./targets/nsis/NsisTarget\"\nimport { AppPackageHelper, CopyElevateHelper } from \"./targets/nsis/nsisUtil\"\nimport { WebInstallerTarget } from \"./targets/nsis/WebInstallerTarget\"\nimport { createCommonTarget } from \"./targets/targetFactory\"\nimport { BuildCacheManager, digest } from \"./util/cacheManager\"\nimport { isBuildCacheEnabled } from \"./util/flags\"\nimport { time } from \"./util/timer\"\nimport { getWindowsVm, VmManager } from \"./vm/vm\"\nimport { execWine } from \"./wine\"\n\nexport class WinPackager extends PlatformPackager<WindowsConfiguration> {\n  _iconPath = new Lazy(() => this.getOrConvertIcon(\"ico\"))\n\n  readonly vm = new Lazy<VmManager>(() => (process.platform === \"win32\" ? Promise.resolve(new VmManager()) : getWindowsVm(this.debugLogger)))\n\n  readonly signingManager = new Lazy(async () => {\n    let manager: SignManager\n    if (this.platformSpecificBuildOptions.azureSignOptions != null) {\n      manager = new WindowsSignAzureManager(this)\n    } else {\n      manager = new WindowsSignToolManager(this)\n    }\n    await manager.initialize()\n    return manager\n  })\n\n  get isForceCodeSigningVerification(): boolean {\n    return this.platformSpecificBuildOptions.verifyUpdateCodeSignature !== false\n  }\n\n  constructor(info: Packager) {\n    super(info, Platform.WINDOWS)\n  }\n\n  get defaultTarget(): Array<string> {\n    return [\"nsis\"]\n  }\n\n  createTargets(targets: Array<string>, mapper: (name: string, factory: (outDir: string) => Target) => void): void {\n    let copyElevateHelper: CopyElevateHelper | null\n    const getCopyElevateHelper = () => {\n      if (copyElevateHelper == null) {\n        copyElevateHelper = new CopyElevateHelper()\n      }\n      return copyElevateHelper\n    }\n\n    let helper: AppPackageHelper | null\n    const getHelper = () => {\n      if (helper == null) {\n        helper = new AppPackageHelper(getCopyElevateHelper())\n      }\n      return helper\n    }\n\n    for (const name of targets) {\n      if (name === DIR_TARGET) {\n        continue\n      }\n\n      if (name === \"nsis\" || name === \"portable\") {\n        mapper(name, outDir => new NsisTarget(this, outDir, name, getHelper()))\n      } else if (name === \"nsis-web\") {\n        // package file format differs from nsis target\n        mapper(name, outDir => new WebInstallerTarget(this, path.join(outDir, name), name, new AppPackageHelper(getCopyElevateHelper())))\n      } else {\n        const targetClass: typeof NsisTarget | typeof AppXTarget | typeof MsiTarget | typeof MsiWrappedTarget | null = (() => {\n          switch (name) {\n            case \"squirrel\":\n              try {\n                return require(\"electron-builder-squirrel-windows\").default\n              } catch (e: any) {\n                throw new InvalidConfigurationError(`Module electron-builder-squirrel-windows must be installed in addition to build Squirrel.Windows: ${e.stack || e}`)\n              }\n\n            case \"appx\":\n              return require(\"./targets/AppxTarget\").default\n\n            case \"msi\":\n              return require(\"./targets/MsiTarget\").default\n\n            case \"msiwrapped\":\n              return require(\"./targets/MsiWrappedTarget\").default\n\n            default:\n              return null\n          }\n        })()\n\n        mapper(name, outDir => (targetClass === null ? createCommonTarget(name, outDir, this) : new (targetClass as any)(this, outDir, name)))\n      }\n    }\n  }\n\n  getIconPath() {\n    return this._iconPath.value\n  }\n\n  doGetCscPassword(): string | Nullish {\n    return chooseNotNull(chooseNotNull(this.platformSpecificBuildOptions.signtoolOptions?.certificatePassword, process.env.WIN_CSC_KEY_PASSWORD), super.doGetCscPassword())\n  }\n\n  async sign(file: string): Promise<boolean> {\n    const signOptions: WindowsSignOptions = {\n      path: file,\n      options: this.platformSpecificBuildOptions,\n    }\n\n    const didSignSuccessfully = await signWindows(signOptions, this)\n    if (!didSignSuccessfully && this.forceCodeSigning) {\n      throw new InvalidConfigurationError(\n        `App is not signed and \"forceCodeSigning\" is set to true, please ensure that code signing configuration is correct, please see https://electron.build/code-signing`\n      )\n    }\n    return didSignSuccessfully\n  }\n\n  async signAndEditResources(file: string, arch: Arch, outDir: string, internalName?: string | null, requestedExecutionLevel?: RequestedExecutionLevel | null) {\n    const appInfo = this.appInfo\n\n    const files: Array<string> = []\n\n    const args = [\n      file,\n      \"--set-version-string\",\n      \"FileDescription\",\n      appInfo.productName,\n      \"--set-version-string\",\n      \"ProductName\",\n      appInfo.productName,\n      \"--set-version-string\",\n      \"LegalCopyright\",\n      appInfo.copyright,\n      \"--set-file-version\",\n      appInfo.shortVersion || appInfo.buildVersion,\n      \"--set-product-version\",\n      appInfo.shortVersionWindows || appInfo.getVersionInWeirdWindowsForm(),\n    ]\n\n    if (internalName != null) {\n      args.push(\"--set-version-string\", \"InternalName\", internalName, \"--set-version-string\", \"OriginalFilename\", \"\")\n    }\n\n    if (requestedExecutionLevel != null && requestedExecutionLevel !== \"asInvoker\") {\n      args.push(\"--set-requested-execution-level\", requestedExecutionLevel)\n    }\n\n    use(appInfo.companyName, it => args.push(\"--set-version-string\", \"CompanyName\", it))\n    use(this.platformSpecificBuildOptions.legalTrademarks, it => args.push(\"--set-version-string\", \"LegalTrademarks\", it))\n    const iconPath = await this.getIconPath()\n    use(iconPath, it => {\n      files.push(it)\n      args.push(\"--set-icon\", it)\n    })\n\n    const config = this.config\n    const cscInfoForCacheDigest = !isBuildCacheEnabled() || isCI || config.electronDist != null ? null : await (await this.signingManager.value).cscInfo.value\n    let buildCacheManager: BuildCacheManager | null = null\n    // resources editing doesn't change executable for the same input and executed quickly - no need to complicate\n    if (cscInfoForCacheDigest != null) {\n      const cscFile = (cscInfoForCacheDigest as FileCodeSigningInfo).file\n      if (cscFile != null) {\n        files.push(cscFile)\n      }\n\n      const timer = time(\"executable cache\")\n      const hash = createHash(\"sha512\")\n      hash.update(config.electronVersion || \"no electronVersion\")\n      hash.update(JSON.stringify(this.platformSpecificBuildOptions))\n      hash.update(JSON.stringify(args))\n      hash.update(this.platformSpecificBuildOptions.signtoolOptions?.certificateSha1 || \"no certificateSha1\")\n      hash.update(this.platformSpecificBuildOptions.signtoolOptions?.certificateSubjectName || \"no subjectName\")\n\n      buildCacheManager = new BuildCacheManager(outDir, file, arch)\n      if (await buildCacheManager.copyIfValid(await digest(hash, files))) {\n        timer.end()\n        return\n      }\n      timer.end()\n    }\n\n    const timer = time(\"wine&sign\")\n    // rcedit crashed of executed using wine, resourcehacker works\n    if (process.platform === \"win32\" || process.platform === \"darwin\") {\n      await executeAppBuilder([\"rcedit\", \"--args\", JSON.stringify(args)], undefined /* child-process */, {}, 3 /* retry three times */)\n    } else if (this.info.framework.name === \"electron\") {\n      const vendorPath = await getSignVendorPath()\n      await execWine(path.join(vendorPath, \"rcedit-ia32.exe\"), path.join(vendorPath, \"rcedit-x64.exe\"), args)\n    }\n\n    await this.sign(file)\n    timer.end()\n\n    if (buildCacheManager != null) {\n      await buildCacheManager.save()\n    }\n  }\n\n  private shouldSignFile(file: string): boolean {\n    const shouldSignExplicit = !!this.platformSpecificBuildOptions.signExts?.some(ext => file.endsWith(ext))\n    return shouldSignExplicit || file.endsWith(\".exe\")\n  }\n\n  protected createTransformerForExtraFiles(packContext: AfterPackContext): FileTransformer | null {\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return null\n    }\n\n    return file => {\n      if (this.shouldSignFile(file)) {\n        const parentDir = path.dirname(file)\n        if (parentDir !== packContext.appOutDir) {\n          return new CopyFileTransformer(file => this.sign(file))\n        }\n      }\n      return null\n    }\n  }\n\n  protected async signApp(packContext: AfterPackContext, isAsar: boolean): Promise<boolean> {\n    const exeFileName = `${this.appInfo.productFilename}.exe`\n    if (this.platformSpecificBuildOptions.signAndEditExecutable === false) {\n      return false\n    }\n\n    const files = await readdir(packContext.appOutDir)\n    for (const file of files) {\n      if (file === exeFileName) {\n        await this.signAndEditResources(\n          path.join(packContext.appOutDir, exeFileName),\n          packContext.arch,\n          packContext.outDir,\n          path.basename(exeFileName, \".exe\"),\n          this.platformSpecificBuildOptions.requestedExecutionLevel\n        )\n      } else if (this.shouldSignFile(file)) {\n        await this.sign(path.join(packContext.appOutDir, file))\n      }\n    }\n\n    if (!isAsar) {\n      return true\n    }\n\n    const filesPromise = (filepath: string[]) => {\n      const outDir = path.join(packContext.appOutDir, ...filepath)\n      return walk(outDir, (file, stat) => stat.isDirectory() || this.shouldSignFile(file))\n    }\n    const filesToSign = await Promise.all([filesPromise([\"resources\", \"app.asar.unpacked\"]), filesPromise([\"swiftshader\"])])\n    for (const file of filesToSign.flat(1)) {\n      await this.sign(file)\n    }\n\n    return true\n  }\n}\n"]}