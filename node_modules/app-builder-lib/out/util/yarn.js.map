{"version": 3, "file": "yarn.js", "sourceRoot": "", "sources": ["../../src/util/yarn.ts"], "names": [], "mappings": ";;AAcA,4CAqBC;AAWD,8BAiCC;AAiDD,wCAiBC;AA4BD,0BAuCC;AAnND,uEAAwE;AAExE,+CAAkD;AAClD,uCAAqC;AAErC,2BAA4B;AAC5B,6BAA4B;AAE5B,6CAA4D;AAC5D,oEAA+E;AAE/E,+CAA4D;AAErD,KAAK,UAAU,gBAAgB,CAAC,MAAqB,EAAE,MAAc,EAAE,OAAuB,EAAE,YAAY,GAAG,KAAK;IACzH,MAAM,gBAAgB,GAAmB;QACvC,eAAe,EAAE,MAAM,CAAC,2BAA2B,KAAK,IAAI;QAC5D,cAAc,EAAE,IAAA,sBAAO,EAAC,MAAM,CAAC,OAAO,CAAC;QACvC,GAAG,OAAO;KACX,CAAA;IACD,IAAI,uBAAuB,GAAG,KAAK,CAAA;IAEnC,KAAK,MAAM,SAAS,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,EAAE,CAAC;QACpD,IAAI,MAAM,IAAA,qBAAU,EAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC;YACnD,uBAAuB,GAAG,IAAI,CAAA;YAE9B,MAAK;QACP,CAAC;IACH,CAAC;IAED,IAAI,YAAY,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC7C,MAAM,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAA;IAC7D,CAAC;SAAM,CAAC;QACN,MAAM,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAA;IACjD,CAAC;AACH,CAAC;AAOD,SAAS,sBAAsB;IAC7B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAA,YAAO,GAAE,EAAE,eAAe,CAAC,CAAA;AAC9C,CAAC;AAED,SAAgB,SAAS,CAAC,aAAmC,EAAE,QAAyB,EAAE,IAAY,EAAE,eAAwB;IAC9H,MAAM,aAAa,GAAG,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAA;IACtD,MAAM,MAAM,GAAQ;QAClB,GAAG,OAAO,CAAC,GAAG;QACd,eAAe,EAAE,aAAa;QAC9B,sBAAsB,EAAE,aAAa;QACrC,mBAAmB,EAAE,QAAQ;QAC7B,4BAA4B,EAAE,eAAe;QAC7C,4BAA4B;QAC5B,0BAA0B,EAAE,QAAQ;QACpC,wBAAwB,EAAE,IAAI;QAC9B,4BAA4B,EAAE,IAAI;KACnC,CAAA;IAED,IAAI,QAAQ,KAAK,OAAO,CAAC,QAAQ,EAAE,CAAC;QAClC,MAAM,CAAC,gBAAgB,GAAG,MAAM,CAAA;IAClC,CAAC;IACD,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,QAAQ,EAAE,CAAC;QAClD,MAAM,CAAC,sBAAsB,GAAG,SAAS,CAAA;IAC3C,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,CAAC;QACjC,OAAO,MAAM,CAAA;IACf,CAAC;IAED,+CAA+C;IAC/C,OAAO;QACL,GAAG,MAAM;QACT,kBAAkB,EAAE,MAAM,CAAC,0BAA0B,IAAI,gCAAgC;QACzF,iBAAiB,EAAE,aAAa,CAAC,OAAO;QACxC,kBAAkB,EAAE,UAAU;QAC9B,iBAAiB,EAAE,sBAAsB,EAAE;KAC5C,CAAA;AACH,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,EAAM;IAClC,IAAI,EAAE,KAAK,MAAM,EAAE,CAAC;QAClB,OAAO,KAAK,CAAA;IACd,CAAC;IACD,MAAM,OAAO,GAAG,MAAM,IAAA,gDAAwB,EAAC,EAAE,CAAC,CAAA;IAClD,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrD,OAAO,KAAK,CAAA;IACd,CAAC;IAED,OAAO,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAA;AACrC,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,MAAqB,EAAE,MAAc,EAAE,OAAuB;IAC/F,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,CAAA;IACrD,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAA;IACzC,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;IAE7C,MAAM,UAAU,GAAG,MAAM,IAAA,kCAAkB,EAAC,MAAM,CAAC,CAAA;IACnD,MAAM,EAAE,GAAG,MAAM,IAAA,8BAAM,EAAC,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC,CAAA;IAC5C,kBAAG,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,oCAAoC,CAAC,CAAA;IAC1F,MAAM,QAAQ,GAAG,CAAC,SAAS,CAAC,CAAA;IAC5B,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,EAAE,CAAC,CAAA;IAC5C,IAAI,CAAC,WAAW,EAAE,CAAC;QACjB,IAAI,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,MAAM,EAAE,CAAC;YAC5C,QAAQ,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA;QACjC,CAAC;IACH,CAAC;IAED,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,EAAE,CAAC;QACvB,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;IACnC,CAAC;IAED,MAAM,QAAQ,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAA;IAEvC,IAAI,cAAc,IAAI,IAAI,EAAE,CAAC;QAC3B,QAAQ,CAAC,IAAI,CAAC,GAAG,cAAc,CAAC,CAAA;IAClC,CAAC;IACD,MAAM,IAAA,oBAAK,EAAC,QAAQ,EAAE,QAAQ,EAAE;QAC9B,GAAG,EAAE,MAAM;QACX,GAAG,EAAE,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI,CAAC;KACxF,CAAC,CAAA;IAEF,2JAA2J;IAC3J,oEAAoE;IACpE,OAAO,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;AACzC,CAAC;AAEM,KAAK,UAAU,cAAc,CAAC,QAAyB,EAAE,IAAY,EAAE,aAAmC;IAC/G,kBAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,4BAA4B,CAAC,CAAA;IAC1D,6CAA6C;IAC7C,MAAM,OAAO,GAAG,WAAW,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;IACvE,MAAM,IAAI,GAAG,CAAC,SAAS,CAAC,CAAA;IACxB,wEAAwE;IACxE,oEAAoE;IACpE,oCAAoC;IACpC,wDAAwD;IACxD,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,aAAa,CAAC,OAAO;SACzC,KAAK,CAAC,GAAG,CAAC;SACV,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;SACX,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;IAC5B,IAAI,KAAK,IAAI,EAAE,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC;QAC9E,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAA;IACrC,CAAC;IACD,MAAM,IAAA,oBAAK,EAAC,OAAO,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,SAAS,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAA;AACrF,CAAC;AAED,SAAS,kBAAkB,CAAC,EAAM;IAChC,IAAI,GAAG,GAAG,EAAE,CAAA;IACZ,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,EAAE,CAAC;QACtC,GAAG,GAAG,MAAM,CAAA;IACd,CAAC;IACD,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAA;AAC9D,CAAC;AAED,SAAS,aAAa,CAAC,EAAM;IAC3B,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAA;IACnD,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,MAAM,IAAI,EAAE,KAAK,MAAM,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAA;AAChH,CAAC;AAcD,gBAAgB;AACT,KAAK,UAAU,OAAO,CAAC,MAAqB,EAAE,MAAc,EAAE,OAAuB;IAC1F,MAAM,aAAa,GAAG;QACpB,YAAY,EAAE,MAAM,OAAO,CAAC,cAAc,CAAC,KAAK;QAChD,YAAY,EAAE,OAAO,CAAC,QAAQ;QAC9B,QAAQ,EAAE,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ;QAC9C,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI;QAClC,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU;QAC5D,eAAe,EAAE,OAAO,CAAC,eAAe,KAAK,IAAI;KAClD,CAAA;IACD,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,aAAa,CAAA;IAEzD,IAAI,MAAM,CAAC,eAAe,KAAK,QAAQ,EAAE,CAAC;QACxC,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,EAAE,eAAe,CAAC,CAAA;QAC7E,OAAO,IAAA,0CAA6B,EAAC,CAAC,sBAAsB,CAAC,EAAE,aAAa,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAA;IACrG,CAAC;IAED,MAAM,EACJ,aAAa,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,GAC5C,GAAG,OAAO,CAAA;IACX,MAAM,OAAO,GAAG;QACd,eAAe;QACf,IAAI;QACJ,eAAe;QACf,MAAM,EAAE,kBAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,IAAI;KACrC,CAAA;IACD,kBAAG,CAAC,IAAI,CAAC,OAAO,EAAE,6BAA6B,CAAC,CAAA;IAEhD,MAAM,cAAc,GAAmC;QACrD,SAAS,EAAE,MAAM;QACjB,eAAe;QACf,IAAI;QACJ,QAAQ;QACR,eAAe;QACf,eAAe,EAAE,MAAM,IAAA,kCAAkB,EAAC,MAAM,CAAC;QACjD,IAAI,EAAG,MAAM,CAAC,eAA+B,IAAI,YAAY;QAC7D,iBAAiB,EAAE,IAAI;KACxB,CAAA;IACD,OAAO,IAAA,iBAAa,EAAC,cAAc,CAAC,CAAA;AACtC,CAAC", "sourcesContent": ["import * as electronRebuild from \"@electron/rebuild\"\nimport { getProjectRootPath } from \"@electron/rebuild/lib/search-module\"\nimport { RebuildMode } from \"@electron/rebuild/lib/types\"\nimport { asArray, log, spawn } from \"builder-util\"\nimport { pathExists } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport { homedir } from \"os\"\nimport * as path from \"path\"\nimport { Configuration } from \"../configuration\"\nimport { executeAppBuilderAndWriteJson } from \"./appBuilder\"\nimport { PM, detect, getPackageManagerVersion } from \"../node-module-collector\"\nimport { NodeModuleDirInfo } from \"./packageDependencies\"\nimport { rebuild as remoteRebuild } from \"./rebuild/rebuild\"\n\nexport async function installOrRebuild(config: Configuration, appDir: string, options: RebuildOptions, forceInstall = false) {\n  const effectiveOptions: RebuildOptions = {\n    buildFromSource: config.buildDependenciesFromSource === true,\n    additionalArgs: asArray(config.npmArgs),\n    ...options,\n  }\n  let isDependenciesInstalled = false\n\n  for (const fileOrDir of [\"node_modules\", \".pnp.js\"]) {\n    if (await pathExists(path.join(appDir, fileOrDir))) {\n      isDependenciesInstalled = true\n\n      break\n    }\n  }\n\n  if (forceInstall || !isDependenciesInstalled) {\n    await installDependencies(config, appDir, effectiveOptions)\n  } else {\n    await rebuild(config, appDir, effectiveOptions)\n  }\n}\n\nexport interface DesktopFrameworkInfo {\n  version: string\n  useCustomDist: boolean\n}\n\nfunction getElectronGypCacheDir() {\n  return path.join(homedir(), \".electron-gyp\")\n}\n\nexport function getGypEnv(frameworkInfo: DesktopFrameworkInfo, platform: NodeJS.Platform, arch: string, buildFromSource: boolean) {\n  const npmConfigArch = arch === \"armv7l\" ? \"arm\" : arch\n  const common: any = {\n    ...process.env,\n    npm_config_arch: npmConfigArch,\n    npm_config_target_arch: npmConfigArch,\n    npm_config_platform: platform,\n    npm_config_build_from_source: buildFromSource,\n    // required for node-pre-gyp\n    npm_config_target_platform: platform,\n    npm_config_update_binary: true,\n    npm_config_fallback_to_build: true,\n  }\n\n  if (platform !== process.platform) {\n    common.npm_config_force = \"true\"\n  }\n  if (platform === \"win32\" || platform === \"darwin\") {\n    common.npm_config_target_libc = \"unknown\"\n  }\n\n  if (!frameworkInfo.useCustomDist) {\n    return common\n  }\n\n  // https://github.com/nodejs/node-gyp/issues/21\n  return {\n    ...common,\n    npm_config_disturl: common.npm_config_electron_mirror || \"https://electronjs.org/headers\",\n    npm_config_target: frameworkInfo.version,\n    npm_config_runtime: \"electron\",\n    npm_config_devdir: getElectronGypCacheDir(),\n  }\n}\n\nasync function checkYarnBerry(pm: PM) {\n  if (pm !== \"yarn\") {\n    return false\n  }\n  const version = await getPackageManagerVersion(pm)\n  if (version == null || version.split(\".\").length < 1) {\n    return false\n  }\n\n  return version.split(\".\")[0] >= \"2\"\n}\n\nasync function installDependencies(config: Configuration, appDir: string, options: RebuildOptions): Promise<any> {\n  const platform = options.platform || process.platform\n  const arch = options.arch || process.arch\n  const additionalArgs = options.additionalArgs\n\n  const projectDir = await getProjectRootPath(appDir)\n  const pm = await detect({ cwd: projectDir })\n  log.info({ pm, platform, arch, projectDir, appDir }, `installing production dependencies`)\n  const execArgs = [\"install\"]\n  const isYarnBerry = await checkYarnBerry(pm)\n  if (!isYarnBerry) {\n    if (process.env.NPM_NO_BIN_LINKS === \"true\") {\n      execArgs.push(\"--no-bin-links\")\n    }\n  }\n\n  if (!isRunningYarn(pm)) {\n    execArgs.push(\"--prefer-offline\")\n  }\n\n  const execPath = getPackageToolPath(pm)\n\n  if (additionalArgs != null) {\n    execArgs.push(...additionalArgs)\n  }\n  await spawn(execPath, execArgs, {\n    cwd: appDir,\n    env: getGypEnv(options.frameworkInfo, platform, arch, options.buildFromSource === true),\n  })\n\n  // Some native dependencies no longer use `install` hook for building their native module, (yarn 3+ removed implicit link of `install` and `rebuild` steps)\n  // https://github.com/electron-userland/electron-builder/issues/8024\n  return rebuild(config, appDir, options)\n}\n\nexport async function nodeGypRebuild(platform: NodeJS.Platform, arch: string, frameworkInfo: DesktopFrameworkInfo) {\n  log.info({ platform, arch }, \"executing node-gyp rebuild\")\n  // this script must be used only for electron\n  const nodeGyp = `node-gyp${process.platform === \"win32\" ? \".cmd\" : \"\"}`\n  const args = [\"rebuild\"]\n  // headers of old Electron versions do not have a valid config.gypi file\n  // and --force-process-config must be passed to node-gyp >= 8.4.0 to\n  // correctly build modules for them.\n  // see also https://github.com/nodejs/node-gyp/pull/2497\n  const [major, minor] = frameworkInfo.version\n    .split(\".\")\n    .slice(0, 2)\n    .map(n => parseInt(n, 10))\n  if (major <= 13 || (major == 14 && minor <= 1) || (major == 15 && minor <= 2)) {\n    args.push(\"--force-process-config\")\n  }\n  await spawn(nodeGyp, args, { env: getGypEnv(frameworkInfo, platform, arch, true) })\n}\n\nfunction getPackageToolPath(pm: PM) {\n  let cmd = pm\n  if (process.env.FORCE_YARN === \"true\") {\n    cmd = \"yarn\"\n  }\n  return `${cmd}${process.platform === \"win32\" ? \".cmd\" : \"\"}`\n}\n\nfunction isRunningYarn(pm: PM) {\n  const userAgent = process.env.npm_config_user_agent\n  return process.env.FORCE_YARN === \"true\" || pm === \"yarn\" || (userAgent != null && /\\byarn\\b/.test(userAgent))\n}\n\nexport interface RebuildOptions {\n  frameworkInfo: DesktopFrameworkInfo\n  productionDeps: Lazy<Array<NodeModuleDirInfo>>\n\n  platform?: NodeJS.Platform\n  arch?: string\n\n  buildFromSource?: boolean\n\n  additionalArgs?: Array<string> | null\n}\n\n/** @internal */\nexport async function rebuild(config: Configuration, appDir: string, options: RebuildOptions) {\n  const configuration = {\n    dependencies: await options.productionDeps.value,\n    nodeExecPath: process.execPath,\n    platform: options.platform || process.platform,\n    arch: options.arch || process.arch,\n    additionalArgs: options.additionalArgs,\n    execPath: process.env.npm_execpath || process.env.NPM_CLI_JS,\n    buildFromSource: options.buildFromSource === true,\n  }\n  const { arch, buildFromSource, platform } = configuration\n\n  if (config.nativeRebuilder === \"legacy\") {\n    const env = getGypEnv(options.frameworkInfo, platform, arch, buildFromSource)\n    return executeAppBuilderAndWriteJson([\"rebuild-node-modules\"], configuration, { env, cwd: appDir })\n  }\n\n  const {\n    frameworkInfo: { version: electronVersion },\n  } = options\n  const logInfo = {\n    electronVersion,\n    arch,\n    buildFromSource,\n    appDir: log.filePath(appDir) || \"./\",\n  }\n  log.info(logInfo, \"executing @electron/rebuild\")\n\n  const rebuildOptions: electronRebuild.RebuildOptions = {\n    buildPath: appDir,\n    electronVersion,\n    arch,\n    platform,\n    buildFromSource,\n    projectRootPath: await getProjectRootPath(appDir),\n    mode: (config.nativeRebuilder as RebuildMode) || \"sequential\",\n    disablePreGypCopy: true,\n  }\n  return remoteRebuild(rebuildOptions)\n}\n"]}