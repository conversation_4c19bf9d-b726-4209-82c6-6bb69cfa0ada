{"version": 3, "file": "packageDependencies.js", "sourceRoot": "", "sources": ["../../src/util/packageDependencies.ts"], "names": [], "mappings": ";;AAGA,4DAWC;AAdD,uCAA+B;AAC/B,6CAAsD;AAEtD,SAAgB,wBAAwB,CAAoB,UAAkB,EAAE,oBAA0C,EAAE,OAAU;IACpI,OAAO,IAAI,eAAI,CAAC,KAAK,IAAI,EAAE;QACzB,MAAM,IAAI,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,CAAC,CAAA;QACnD,IAAI,OAAO;YAAE,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QACnC,IAAI,oBAAoB,IAAI,IAAI,EAAE,CAAC;YACjC,KAAK,MAAM,IAAI,IAAI,oBAAoB,EAAE,CAAC;gBACxC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YAClC,CAAC;QACH,CAAC;QACD,OAAO,IAAA,oCAAuB,EAA6D,IAAI,CAAC,CAAA;IAClG,CAAC,CAAC,CAAA;AACJ,CAAC", "sourcesContent": ["import { <PERSON>zy } from \"lazy-val\"\nimport { executeAppBuilderAsJson } from \"./appBuilder\"\n\nexport function createLazyProductionDeps<T extends boolean>(projectDir: string, excludedDependencies: Array<string> | null, flatten: T) {\n  return new Lazy(async () => {\n    const args = [\"node-dep-tree\", \"--dir\", projectDir]\n    if (flatten) args.push(\"--flatten\")\n    if (excludedDependencies != null) {\n      for (const name of excludedDependencies) {\n        args.push(\"--exclude-dep\", name)\n      }\n    }\n    return executeAppBuilderAsJson<Array<T extends true ? NodeModuleInfo : NodeModuleDirInfo>>(args)\n  })\n}\n\nexport interface NodeModuleDirInfo {\n  readonly dir: string\n  readonly deps: Array<NodeModuleInfo>\n}\n\nexport interface NodeModuleInfo {\n  readonly name: string\n  readonly version: string\n  readonly dir: string\n  readonly dependencies?: Array<NodeModuleInfo>\n}\n"]}