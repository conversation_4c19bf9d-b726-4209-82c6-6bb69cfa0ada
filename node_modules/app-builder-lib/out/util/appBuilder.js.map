{"version": 3, "file": "appBuilder.js", "sourceRoot": "", "sources": ["../../src/util/appBuilder.ts"], "names": [], "mappings": ";;AAGA,0DAYC;AAED,sEAWC;AAED,oCAOC;AArCD,+CAAgD;AAGhD,SAAgB,uBAAuB,CAAI,IAAmB;IAC5D,OAAO,IAAA,gCAAiB,EAAC,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;QAC9C,IAAI,SAAS,KAAK,EAAE,EAAE,CAAC;YACrB,OAAO,MAAM,CAAC,MAAM,CAAC,IAAI,CAAM,CAAA;QACjC,CAAC;QAED,IAAI,CAAC;YACH,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAM,CAAA;QACnC,CAAC;QAAC,OAAO,CAAM,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,OAAO,MAAM,SAAS,GAAG,CAAC,CAAA;QACtE,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAED,SAAgB,6BAA6B,CAAC,IAAmB,EAAE,IAAS,EAAE,eAA6B,EAAE;IAC3G,OAAO,IAAA,gCAAiB,EACtB,IAAI,EACJ,YAAY,CAAC,EAAE;QACb,YAAY,CAAC,KAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAA;IAC/C,CAAC,EACD;QACE,GAAG,YAAY;QACf,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC;KACxC,CACF,CAAA;AACH,CAAC;AAED,SAAgB,YAAY,CAAC,EAAiB,EAAE,cAA6C;IAC3F,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,CAAC;QAC/C,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC,CAAA;QAClC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,KAAK,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;AACH,CAAC", "sourcesContent": ["import { executeAppBuilder } from \"builder-util\"\nimport { SpawnOptions } from \"child_process\"\n\nexport function executeAppBuilderAsJson<T>(args: Array<string>): Promise<T> {\n  return executeAppBuilder(args).then(rawResult => {\n    if (rawResult === \"\") {\n      return Object.create(null) as T\n    }\n\n    try {\n      return JSON.parse(rawResult) as T\n    } catch (e: any) {\n      throw new Error(`Cannot parse result: ${e.message}: \"${rawResult}\"`)\n    }\n  })\n}\n\nexport function executeAppBuilderAndWriteJson(args: Array<string>, data: any, extraOptions: SpawnOptions = {}): Promise<string> {\n  return executeAppBuilder(\n    args,\n    childProcess => {\n      childProcess.stdin!.end(JSON.stringify(data))\n    },\n    {\n      ...extraOptions,\n      stdio: [\"pipe\", \"pipe\", process.stdout],\n    }\n  )\n}\n\nexport function objectToArgs(to: Array<string>, argNameToValue: Record<string, string | null>): void {\n  for (const name of Object.keys(argNameToValue)) {\n    const value = argNameToValue[name]\n    if (value != null) {\n      to.push(`--${name}`, value)\n    }\n  }\n}\n"]}