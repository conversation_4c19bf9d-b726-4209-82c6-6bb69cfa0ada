{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../../src/util/config/config.ts"], "names": [], "mappings": ";;AAmCA,8BA+CC;AA2GD,wCAgBC;AAaD,sDA4CC;AAID,gEAuBC;AAjSD,+CAAqH;AAErH,uCAAmC;AACnC,uCAA+B;AAC/B,6BAA4B;AAG5B,mDAAgD;AAChD,2CAA+C;AAC/C,iCAA2G;AAC3G,MAAM,cAAc,GAAG,OAAO,CAAC,uBAAuB,CAAC,CAAA;AAEvD,oEAAoE;AACpE,SAAS,YAAY,CAAC,MAAqB,EAAE,iBAAgC;IAC3E,kHAAkH;IAClH,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAA;IAChF,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAQ,iBAAyB,CAAC,OAAO,CAAA;IAC3C,CAAC;IAED,IAAA,yBAAU,EAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;IAErC,IAAI,OAAO,IAAI,IAAI,EAAE,CAAC;QACpB,OAAM;IACR,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAqB,CAAA;IAC/C,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC5B,MAAM,CAAC,OAAO,GAAG,OAAO,CAAA;IAC1B,CAAC;SAAM,CAAC;QACN,iBAAiB;QACjB,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAA;IACvC,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,SAAS,CAC7B,UAAkB,EAClB,UAAyB,EACzB,iBAA0C,EAC1C,kBAAoD,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAA,2BAAoB,EAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;IAEzI,MAAM,aAAa,GAAsB,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,EAAE,kBAAkB,EAAE,UAAU,EAAE,eAAe,EAAE,CAAA;IACjI,MAAM,sBAAsB,GAAG,MAAM,IAAA,gBAAU,EAAgB,aAAa,EAAE,UAAU,CAAC,CAAA;IACzF,MAAM,MAAM,GAAG,sBAAsB,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,sBAAsB,CAAC,MAAM,CAAA;IAClF,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC9B,YAAY,CAAC,MAAM,EAAE,iBAAiB,CAAC,CAAA;IACzC,CAAC;IAED,IAAI,sBAAsB,IAAI,IAAI,EAAE,CAAC;QACnC,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,sBAAsB,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC,CAAC,sBAAsB,CAAC,UAAU,EAAE,EAAE,sBAAsB,CAAC,CAAA;IAC5J,CAAC;IAED,IAAI,MAAM,CAAC,OAAO,IAAI,IAAI,IAAI,MAAM,CAAC,OAAO,KAAK,IAAI,EAAE,CAAC;QACtD,MAAM,QAAQ,GAAG,CAAC,MAAM,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;QACpD,MAAM,eAAe,GAAG,QAAQ,CAAC,eAAe,CAAA;QAChD,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAA;QAC1C,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,eAAe,IAAI,YAAY,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,eAAe,IAAI,eAAe,CAAC,EAAE,CAAC;YACjI,MAAM,CAAC,OAAO,GAAG,WAAW,CAAA;QAC9B,CAAC;aAAM,IAAI,eAAe,IAAI,IAAI,IAAI,kBAAkB,IAAI,eAAe,EAAE,CAAC;YAC5E,IAAI,IAAI,GAAG,0CAA0C,CAAA;YACrD,IAAI,CAAC;gBACH,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YAC9B,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC,uCAAuC,CAAC,CAAA;YACjE,CAAC;YACD,MAAM,CAAC,OAAO,GAAG,QAAQ,IAAI,EAAE,CAAA;QACjC,CAAC;IACH,CAAC;IAED,MAAM,aAAa,GAAG,MAAM,4BAA4B,CAAC,MAAM,CAAC,OAAO,EAAE,KAAK,EAAC,YAAY,EAAC,EAAE;QAC5F,IAAI,YAAY,KAAK,WAAW,EAAE,CAAC;YACjC,MAAM,MAAM,GAAG,MAAM,IAAA,kBAAQ,EAAC,UAAU,CAAC,CAAA;YACzC,kBAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,6BAA6B,CAAC,CAAA;YACjE,OAAO,MAAM,CAAA;QACf,CAAC;aAAM,CAAC;YACN,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,MAAM,IAAA,uBAAgB,EAAgB,aAAa,EAAE,YAAY,CAAC,CAAA;YACjG,kBAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,6BAA6B,CAAC,CAAA;YAC7D,OAAO,MAAM,CAAA;QACf,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,OAAO,cAAc,CAAC,CAAC,GAAG,aAAa,EAAE,MAAM,CAAC,CAAC,CAAA;AACnD,CAAC;AAED,SAAS,OAAO,CAAC,KAAkC;IACjD,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAA;AAChF,CAAC;AAED,KAAK,UAAU,4BAA4B,CAAC,aAAuC,EAAE,MAAwD;IAC3I,MAAM,OAAO,GAAG,EAAE,CAAA;IAElB,KAAK,MAAM,YAAY,IAAI,OAAO,CAAC,aAAa,CAAC,EAAE,CAAC;QAClD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,CAAA;QACzC,MAAM,aAAa,GAAG,MAAM,4BAA4B,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QAChF,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,EAAE,MAAM,CAAC,CAAA;IACxC,CAAC;IAED,OAAO,OAAO,CAAA;AAChB,CAAC;AAED,2BAA2B;AAC3B,SAAS,cAAc,CAAC,aAA4B,EAAE,IAA+C;IACnG,IAAI,KAAK,GAAG,aAAa,CAAC,IAAI,CAAC,CAAA;IAC/B,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;QAClB,OAAM;IACR,CAAC;IAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;QAC1B,KAAK,GAAG,CAAC,KAAK,CAAC,CAAA;IACjB,CAAC;IAED,QAAQ,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAChD,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAA;QACnB,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,kCAAkC;YAClC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBACZ,IAAI,aAAa,GAAG,CAAC,GAAG,CAAC,CAAA;gBACzB,IAAI,QAAiB,CAAA;gBACrB,GAAG,CAAC;oBACF,QAAQ,GAAG,KAAK,CAAC,aAAa,EAAE,CAAY,CAAA;gBAC9C,CAAC,QAAQ,QAAQ,IAAI,IAAI,EAAC;gBAE1B,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI,IAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC;oBACjD,IAAI,QAAQ,CAAC,MAAM,IAAI,IAAI,EAAE,CAAC;wBAC5B,QAAQ,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAA;oBAC1B,CAAC;yBAAM,CAAC;wBACN,CAAC;wBAAC,QAAQ,CAAC,MAAwB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;oBAChD,CAAC;oBACD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAW,CAAA;oBACtB,SAAS,QAAQ,CAAA;gBACnB,CAAC;YACH,CAAC;YAED,IAAI,GAAG;gBACL,MAAM,EAAE,CAAC,IAAI,CAAC;aACf,CAAA;YACD,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;QACjB,CAAC;aAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,GAAG,IAAI,kEAAkE,CAAC,KAAK,IAAI,EAAE,CAAC,CAAA;QACxG,CAAC;QAED,4EAA4E;QAC5E,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;YACtB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAA;QACvB,CAAC;QAED,IAAI,IAAI,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC;YACpB,IAAI,CAAC,EAAE,GAAG,SAAS,CAAA;QACrB,CAAC;QAED,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YAC3D,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC7B,CAAC;IACH,CAAC;IAED,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,IAAI,CAAC,CAAA;AACtD,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAc,EAAE,KAAc;IACtD,OAAO,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAA;AAC3D,CAAC;AAID,SAAS,YAAY,CAAC,KAAa,EAAE,KAAa;IAChD,OAAO,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAA;AAC9C,CAAC;AAED,SAAS,aAAa,CAAC,KAAkB;IACvC,MAAM,MAAM,GAAc,EAAE,CAAA;IAE5B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;YACxB,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAA;YAChE,IAAI,YAAY,EAAE,CAAC;gBACjB,YAAY,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,MAAM,CAAC,CAAA;YACtE,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YACnB,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,MAAM,CAAA;AACf,CAAC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAAC,OAAwB;IACrD,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC7B,cAAc,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAC/B,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QACpC,cAAc,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;IAC1C,CAAC;IAED,MAAM,MAAM,GAAG,IAAA,yBAAU,EAAC,gBAAgB,EAAE,EAAE,GAAG,OAAO,CAAC,CAAA;IAEzD,8EAA8E;IAC9E,0EAA0E;IAC1E,6BAA6B;IAC7B,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAA;IAEnC,MAAM,CAAC,KAAK,GAAG,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,WAAC,OAAA,CAAC,MAAA,MAAM,CAAC,KAAK,mCAAI,EAAE,CAAc,CAAA,EAAA,CAAC,CAAC,CAAA;IACtF,OAAO,MAAM,CAAA;AACf,CAAC;AAED,SAAS,gBAAgB;IACvB,OAAO;QACL,WAAW,EAAE;YACX,MAAM,EAAE,MAAM;YACd,cAAc,EAAE,OAAO;SACxB;KACF,CAAA;AACH,CAAC;AAED,MAAM,iBAAiB,GAAG,IAAI,eAAI,CAAC,GAAG,EAAE,CAAC,IAAA,mBAAQ,EAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,CAAA;AAElG,KAAK,UAAU,qBAAqB,CAAC,MAAqB,EAAE,WAAwB;IACzF,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAA;IAC1C,IAAI,aAAa,IAAI,IAAI,EAAE,CAAC;QAC1B,IAAI,aAAa,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,wCAAyB,CAAC,iDAAiD,CAAC,CAAA;QACxF,CAAC;QACD,IAAI,aAAa,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACtC,MAAM,IAAI,wCAAyB,CAAC,mEAAmE,CAAC,CAAA;QAC1G,CAAC;IACH,CAAC;IAED,MAAM,SAAS,GAAQ,MAAM,CAAA;IAC7B,IAAI,SAAS,CAAC,sBAAsB,KAAK,KAAK,EAAE,CAAC;QAC/C,MAAM,IAAI,wCAAyB,CAAC,+EAA+E,CAAC,CAAA;IACtH,CAAC;IACD,IAAI,SAAS,CAAC,QAAQ,IAAI,IAAI,IAAI,SAAS,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,EAAE,CAAC;QAC/E,MAAM,IAAI,wCAAyB,CAAC,4HAA4H,CAAC,CAAA;IACnK,CAAC;IAED,qCAAqC;IACrC,cAAc,CAAC,MAAM,iBAAiB,CAAC,KAAK,EAAE,MAAM,EAAE;QACpD,IAAI,EAAE,oBAAoB,yBAAe,EAAE;QAC3C,aAAa,EAAE,CAAC,cAAsB,EAAE,KAAU,EAAU,EAAE;YAC5D,IAAI,WAAW,CAAC,SAAS,EAAE,CAAC;gBAC1B,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,IAAA,gCAAiB,EAAC,KAAK,CAAC,CAAC,CAAA;YAC5D,CAAC;YAED,MAAM,IAAI,GAAG,4BAA4B,CAAA;YACzC,IAAI,GAAG,GAAG,GAAG,IAAI,gBAAgB,CAAA;YACjC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,CAAA;YACzG,MAAM,QAAQ,GAAW,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAA;YACvE,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAA;YACrF,IAAI,UAAU,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAClD,GAAG,GAAG,GAAG,IAAI,IAAI,UAAU,EAAE,CAAA;YAC/B,CAAC;YAED,OAAO,GAAG,cAAc;YAClB,GAAG;;;;CAId,CAAA;QACG,CAAC;KACF,CAAC,CAAA;AACJ,CAAC;AAED,MAAM,qBAAqB,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;AAErC,KAAK,UAAU,0BAA0B,CAAC,UAAkB,EAAE,UAA4B;IAC/F,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC,CAAA;QACzD,MAAM,IAAI,GAAG,MAAM,IAAA,yBAAU,EAAC,YAAY,CAAC,CAAA;QAC3C,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,MAAM,IAAI,wCAAyB,CAAC,yBAAyB,UAAU,gBAAgB,CAAC,CAAA;QAC1F,CAAC;aAAM,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YAC/B,MAAM,IAAI,wCAAyB,CAAC,yBAAyB,UAAU,qBAAqB,CAAC,CAAA;QAC/F,CAAC;aAAM,IAAI,UAAU,KAAK,YAAY,EAAE,CAAC;YACvC,kBAAG,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,UAAU,EAAE,EAAE,4FAA4F,CAAC,CAAA;QACtI,CAAC;QACD,OAAO,YAAY,CAAA;IACrB,CAAC;IAED,KAAK,MAAM,GAAG,IAAI,qBAAqB,EAAE,CAAC;QACxC,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;QAC/C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,cAAc,CAAC,CAAA;QAC3D,MAAM,IAAI,GAAG,MAAM,IAAA,yBAAU,EAAC,WAAW,CAAC,CAAA;QAC1C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAClC,OAAO,YAAY,CAAA;QACrB,CAAC;IACH,CAAC;IACD,OAAO,UAAU,CAAA;AACnB,CAAC", "sourcesContent": ["import { Debug<PERSON>ogger, deepAssign, InvalidConfigurationError, log, safeStringify<PERSON><PERSON>, statOr<PERSON>ull } from \"builder-util\"\nimport { Null<PERSON> } from \"builder-util-runtime\"\nimport { readJ<PERSON> } from \"fs-extra\"\nimport { Lazy } from \"lazy-val\"\nimport * as path from \"path\"\nimport { Configuration } from \"../../configuration\"\nimport { FileSet } from \"../../options/PlatformSpecificBuildOptions\"\nimport { reactCra } from \"../../presets/rectCra\"\nimport { PACKAGE_VERSION } from \"../../version\"\nimport { getConfig as _getConfig, loadParentConfig, orNullIfFileNotExist, ReadConfigRequest } from \"./load\"\nconst validateSchema = require(\"@develar/schema-utils\")\n\n// https://github.com/electron-userland/electron-builder/issues/1847\nfunction mergePublish(config: Configuration, configFromOptions: Configuration) {\n  // if config from disk doesn't have publish (or object), no need to handle, it will be simply merged by deepAssign\n  const publish = Array.isArray(config.publish) ? configFromOptions.publish : null\n  if (publish != null) {\n    delete (configFromOptions as any).publish\n  }\n\n  deepAssign(config, configFromOptions)\n\n  if (publish == null) {\n    return\n  }\n\n  const listOnDisk = config.publish as Array<any>\n  if (listOnDisk.length === 0) {\n    config.publish = publish\n  } else {\n    // apply to first\n    Object.assign(listOnDisk[0], publish)\n  }\n}\n\nexport async function getConfig(\n  projectDir: string,\n  configPath: string | null,\n  configFromOptions: Configuration | Nullish,\n  packageMetadata: Lazy<Record<string, any> | null> = new Lazy(() => orNullIfFileNotExist(readJson(path.join(projectDir, \"package.json\"))))\n): Promise<Configuration> {\n  const configRequest: ReadConfigRequest = { packageKey: \"build\", configFilename: \"electron-builder\", projectDir, packageMetadata }\n  const configAndEffectiveFile = await _getConfig<Configuration>(configRequest, configPath)\n  const config = configAndEffectiveFile == null ? {} : configAndEffectiveFile.result\n  if (configFromOptions != null) {\n    mergePublish(config, configFromOptions)\n  }\n\n  if (configAndEffectiveFile != null) {\n    log.info({ file: configAndEffectiveFile.configFile == null ? 'package.json (\"build\" field)' : configAndEffectiveFile.configFile }, \"loaded configuration\")\n  }\n\n  if (config.extends == null && config.extends !== null) {\n    const metadata = (await packageMetadata.value) || {}\n    const devDependencies = metadata.devDependencies\n    const dependencies = metadata.dependencies\n    if ((dependencies != null && \"react-scripts\" in dependencies) || (devDependencies != null && \"react-scripts\" in devDependencies)) {\n      config.extends = \"react-cra\"\n    } else if (devDependencies != null && \"electron-webpack\" in devDependencies) {\n      let file = \"electron-webpack/out/electron-builder.js\"\n      try {\n        file = require.resolve(file)\n      } catch (_ignore) {\n        file = require.resolve(\"electron-webpack/electron-builder.yml\")\n      }\n      config.extends = `file:${file}`\n    }\n  }\n\n  const parentConfigs = await loadParentConfigsRecursively(config.extends, async configExtend => {\n    if (configExtend === \"react-cra\") {\n      const result = await reactCra(projectDir)\n      log.info({ preset: configExtend }, \"loaded parent configuration\")\n      return result\n    } else {\n      const { configFile, result } = await loadParentConfig<Configuration>(configRequest, configExtend)\n      log.info({ file: configFile }, \"loaded parent configuration\")\n      return result\n    }\n  })\n\n  return doMergeConfigs([...parentConfigs, config])\n}\n\nfunction asArray(value: string[] | string | Nullish): string[] {\n  return Array.isArray(value) ? value : typeof value === \"string\" ? [value] : []\n}\n\nasync function loadParentConfigsRecursively(configExtends: Configuration[\"extends\"], loader: (configExtend: string) => Promise<Configuration>): Promise<Configuration[]> {\n  const configs = []\n\n  for (const configExtend of asArray(configExtends)) {\n    const result = await loader(configExtend)\n    const parentConfigs = await loadParentConfigsRecursively(result.extends, loader)\n    configs.push(...parentConfigs, result)\n  }\n\n  return configs\n}\n\n// normalize for easy merge\nfunction normalizeFiles(configuration: Configuration, name: \"files\" | \"extraFiles\" | \"extraResources\") {\n  let value = configuration[name]\n  if (value == null) {\n    return\n  }\n\n  if (!Array.isArray(value)) {\n    value = [value]\n  }\n\n  itemLoop: for (let i = 0; i < value.length; i++) {\n    let item = value[i]\n    if (typeof item === \"string\") {\n      // merge with previous if possible\n      if (i !== 0) {\n        let prevItemIndex = i - 1\n        let prevItem: FileSet\n        do {\n          prevItem = value[prevItemIndex--] as FileSet\n        } while (prevItem == null)\n\n        if (prevItem.from == null && prevItem.to == null) {\n          if (prevItem.filter == null) {\n            prevItem.filter = [item]\n          } else {\n            ;(prevItem.filter as Array<string>).push(item)\n          }\n          value[i] = null as any\n          continue itemLoop\n        }\n      }\n\n      item = {\n        filter: [item],\n      }\n      value[i] = item\n    } else if (Array.isArray(item)) {\n      throw new Error(`${name} configuration is invalid, nested array not expected for index ${i}: ${item}`)\n    }\n\n    // make sure that merge logic is not complex - unify different presentations\n    if (item.from === \".\") {\n      item.from = undefined\n    }\n\n    if (item.to === \".\") {\n      item.to = undefined\n    }\n\n    if (item.filter != null && typeof item.filter === \"string\") {\n      item.filter = [item.filter]\n    }\n  }\n\n  configuration[name] = value.filter(it => it != null)\n}\n\nfunction isSimilarFileSet(value: FileSet, other: FileSet): boolean {\n  return value.from === other.from && value.to === other.to\n}\n\ntype Filter = FileSet[\"filter\"]\n\nfunction mergeFilters(value: Filter, other: Filter): string[] {\n  return asArray(value).concat(asArray(other))\n}\n\nfunction mergeFileSets(lists: FileSet[][]): FileSet[] {\n  const result: FileSet[] = []\n\n  for (const list of lists) {\n    for (const item of list) {\n      const existingItem = result.find(i => isSimilarFileSet(i, item))\n      if (existingItem) {\n        existingItem.filter = mergeFilters(item.filter, existingItem.filter)\n      } else {\n        result.push(item)\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * `doMergeConfigs` takes configs in the order you would pass them to\n * Object.assign as sources.\n */\nexport function doMergeConfigs(configs: Configuration[]): Configuration {\n  for (const config of configs) {\n    normalizeFiles(config, \"files\")\n    normalizeFiles(config, \"extraFiles\")\n    normalizeFiles(config, \"extraResources\")\n  }\n\n  const result = deepAssign(getDefaultConfig(), ...configs)\n\n  // `deepAssign` prioritises latter configs, while `mergeFilesSets` prioritises\n  // former configs, so we have to reverse the order, because latter configs\n  // must have higher priority.\n  configs = configs.slice().reverse()\n\n  result.files = mergeFileSets(configs.map(config => (config.files ?? []) as FileSet[]))\n  return result\n}\n\nfunction getDefaultConfig(): Configuration {\n  return {\n    directories: {\n      output: \"dist\",\n      buildResources: \"build\",\n    },\n  }\n}\n\nconst schemeDataPromise = new Lazy(() => readJson(path.join(__dirname, \"..\", \"..\", \"..\", \"scheme.json\")))\n\nexport async function validateConfiguration(config: Configuration, debugLogger: DebugLogger) {\n  const extraMetadata = config.extraMetadata\n  if (extraMetadata != null) {\n    if (extraMetadata.build != null) {\n      throw new InvalidConfigurationError(`--em.build is deprecated, please specify as -c\"`)\n    }\n    if (extraMetadata.directories != null) {\n      throw new InvalidConfigurationError(`--em.directories is deprecated, please specify as -c.directories\"`)\n    }\n  }\n\n  const oldConfig: any = config\n  if (oldConfig.npmSkipBuildFromSource === false) {\n    throw new InvalidConfigurationError(`npmSkipBuildFromSource is deprecated, please use buildDependenciesFromSource\"`)\n  }\n  if (oldConfig.appImage != null && oldConfig.appImage.systemIntegration != null) {\n    throw new InvalidConfigurationError(`appImage.systemIntegration is deprecated, https://github.com/TheAssassin/AppImageLauncher is used for desktop integration\"`)\n  }\n\n  // noinspection JSUnusedGlobalSymbols\n  validateSchema(await schemeDataPromise.value, config, {\n    name: `electron-builder ${PACKAGE_VERSION}`,\n    postFormatter: (formattedError: string, error: any): string => {\n      if (debugLogger.isEnabled) {\n        debugLogger.add(\"invalidConfig\", safeStringifyJson(error))\n      }\n\n      const site = \"https://www.electron.build\"\n      let url = `${site}/configuration`\n      const targets = new Set([\"mac\", \"dmg\", \"pkg\", \"mas\", \"win\", \"nsis\", \"appx\", \"linux\", \"appimage\", \"snap\"])\n      const dataPath: string = error.dataPath == null ? null : error.dataPath\n      const targetPath = dataPath.startsWith(\".\") ? dataPath.substr(1).toLowerCase() : null\n      if (targetPath != null && targets.has(targetPath)) {\n        url = `${site}/${targetPath}`\n      }\n\n      return `${formattedError}\\n  How to fix:\n  1. Open ${url}\n  2. Search the option name on the page (or type in into Search to find across the docs).\n    * Not found? The option was deprecated or not exists (check spelling).\n    * Found? Check that the option in the appropriate place. e.g. \"title\" only in the \"dmg\", not in the root.\n`\n    },\n  })\n}\n\nconst DEFAULT_APP_DIR_NAMES = [\"app\", \"www\"]\n\nexport async function computeDefaultAppDirectory(projectDir: string, userAppDir: string | Nullish): Promise<string> {\n  if (userAppDir != null) {\n    const absolutePath = path.resolve(projectDir, userAppDir)\n    const stat = await statOrNull(absolutePath)\n    if (stat == null) {\n      throw new InvalidConfigurationError(`Application directory ${userAppDir} doesn't exist`)\n    } else if (!stat.isDirectory()) {\n      throw new InvalidConfigurationError(`Application directory ${userAppDir} is not a directory`)\n    } else if (projectDir === absolutePath) {\n      log.warn({ appDirectory: userAppDir }, `Specified application directory equals to project dir — superfluous or wrong configuration`)\n    }\n    return absolutePath\n  }\n\n  for (const dir of DEFAULT_APP_DIR_NAMES) {\n    const absolutePath = path.join(projectDir, dir)\n    const packageJson = path.join(absolutePath, \"package.json\")\n    const stat = await statOrNull(packageJson)\n    if (stat != null && stat.isFile()) {\n      return absolutePath\n    }\n  }\n  return projectDir\n}\n"]}