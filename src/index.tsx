import React from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

// Extend the Window interface to include electronAPI
declare global {
  interface Window {
    electronAPI: {
      readFile: (filePath: string) => Promise<{ success: boolean; content?: string; error?: string }>;
      writeFile: (filePath: string, content: string) => Promise<{ success: boolean; error?: string }>;
      saveFileDialog: () => Promise<{ canceled: boolean; filePath?: string }>;
      readDirectory: (dirPath: string) => Promise<{ success: boolean; items?: any[]; error?: string }>;
      onMenuNewFile: (callback: () => void) => void;
      onMenuOpenFile: (callback: (event: any, filePath: string) => void) => void;
      onMenuSaveFile: (callback: () => void) => void;
      removeAllListeners: (channel: string) => void;
      executePython: (code: string) => Promise<any>;
      getCwd: () => string;
      joinPath: (...paths: string[]) => string;
      dirname: (path: string) => string;
      basename: (path: string) => string;
      extname: (path: string) => string;
    };
  }
}

const container = document.getElementById('root');
if (!container) {
  throw new Error('Root element not found');
}

const root = createRoot(container);
root.render(<App />);
