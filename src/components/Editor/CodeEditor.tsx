import React, { useRef, useEffect } from 'react';
import styled from 'styled-components';
import Editor from '@monaco-editor/react';
import { FileTab } from '../../App';

// Language detection utility
const getLanguageFromExtension = (filename: string): string => {
  const ext = filename.toLowerCase().split('.').pop();

  const languageMap: { [key: string]: string } = {
    'py': 'python',
    'pyw': 'python',
    'pyi': 'python',
    'js': 'javascript',
    'jsx': 'javascript',
    'mjs': 'javascript',
    'ts': 'typescript',
    'tsx': 'typescript',
    'html': 'html',
    'htm': 'html',
    'xhtml': 'html',
    'css': 'css',
    'scss': 'scss',
    'sass': 'scss',
    'less': 'less',
    'json': 'json',
    'xml': 'xml',
    'md': 'markdown',
    'markdown': 'markdown',
    'yml': 'yaml',
    'yaml': 'yaml',
    'sh': 'shell',
    'bash': 'shell',
    'zsh': 'shell',
    'fish': 'shell',
    'sql': 'sql',
    'php': 'php',
    'rb': 'ruby',
    'go': 'go',
    'rs': 'rust',
    'java': 'java',
    'c': 'c',
    'cpp': 'cpp',
    'h': 'c',
    'hpp': 'cpp'
  };

  return languageMap[ext || ''] || 'plaintext';
};

// Enhanced Monaco Editor configuration
const EDITOR_THEMES = {
  'dark-theme': {
    base: 'vs-dark',
    inherit: true,
    rules: [
      // Python
      { token: 'keyword.python', foreground: '569CD6', fontStyle: 'bold' },
      { token: 'string.python', foreground: 'CE9178' },
      { token: 'comment.python', foreground: '6A9955', fontStyle: 'italic' },
      { token: 'number.python', foreground: 'B5CEA8' },
      { token: 'decorator.python', foreground: 'DCDCAA' },

      // JavaScript/TypeScript
      { token: 'keyword.js', foreground: '569CD6', fontStyle: 'bold' },
      { token: 'string.js', foreground: 'CE9178' },
      { token: 'comment.js', foreground: '6A9955', fontStyle: 'italic' },
      { token: 'number.js', foreground: 'B5CEA8' },
      { token: 'type.identifier.ts', foreground: '4EC9B0' },

      // HTML
      { token: 'tag.html', foreground: '569CD6' },
      { token: 'attribute.name.html', foreground: '92C5F8' },
      { token: 'attribute.value.html', foreground: 'CE9178' },
      { token: 'comment.html', foreground: '6A9955', fontStyle: 'italic' },

      // CSS
      { token: 'property.css', foreground: '9CDCFE' },
      { token: 'property.value.css', foreground: 'CE9178' },
      { token: 'selector.css', foreground: 'D7BA7D' },
      { token: 'comment.css', foreground: '6A9955', fontStyle: 'italic' },

      // General
      { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
      { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
      { token: 'string', foreground: 'CE9178' },
      { token: 'number', foreground: 'B5CEA8' },
      { token: 'type', foreground: '4EC9B0' },
      { token: 'class', foreground: '4EC9B0' },
      { token: 'function', foreground: 'DCDCAA' },
      { token: 'variable', foreground: '9CDCFE' },
      { token: 'operator', foreground: 'D4D4D4' },
      { token: 'delimiter', foreground: 'D4D4D4' }
    ],
    colors: {
      'editor.background': '#1e1e1e',
      'editor.foreground': '#d4d4d4',
      'editor.lineHighlightBackground': '#2a2d2e',
      'editor.selectionBackground': '#264f78',
      'editor.inactiveSelectionBackground': '#3a3d41',
      'editorCursor.foreground': '#d4d4d4',
      'editorLineNumber.foreground': '#858585',
      'editorLineNumber.activeForeground': '#c6c6c6',
      'editorGutter.background': '#1e1e1e',
      'editorWhitespace.foreground': '#404040',
      'editorIndentGuide.background': '#404040',
      'editorIndentGuide.activeBackground': '#707070',
      'editorRuler.foreground': '#5a5a5a',
      'scrollbar.shadow': '#000000',
      'scrollbarSlider.background': '#79797966',
      'scrollbarSlider.hoverBackground': '#646464b3',
      'scrollbarSlider.activeBackground': '#bfbfbf66',
      'editorBracketMatch.background': '#0064001a',
      'editorBracketMatch.border': '#888888'
    }
  }
};

const EditorContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: ${props => props.theme.colors.editor.background};
`;

const TabBar = styled.div`
  display: flex;
  background-color: ${props => props.theme.colors.secondary};
  border-bottom: 1px solid ${props => props.theme.colors.border.primary};
  overflow-x: auto;
  min-height: 35px;
`;

const Tab = styled.div<{ isActive: boolean; isDirty: boolean }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.lg};
  background-color: ${props => props.isActive
    ? props.theme.colors.editor.background
    : props.theme.colors.secondary};
  border-right: 1px solid ${props => props.theme.colors.border.primary};
  cursor: pointer;
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.primary};
  min-width: 120px;
  max-width: 200px;
  position: relative;
  user-select: none;

  &:hover {
    background-color: ${props => props.isActive
      ? props.theme.colors.editor.background
      : props.theme.colors.tertiary};
  }

  ${props => props.isDirty && `
    &::after {
      content: '●';
      color: ${props.theme.colors.text.muted};
      margin-left: ${props.theme.spacing.sm};
      font-size: 12px;
    }
  `}
`;

const TabName = styled.span`
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.theme.colors.text.muted};
  cursor: pointer;
  padding: 2px;
  margin-left: ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: 14px;
  line-height: 1;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background-color: ${props => props.theme.colors.quaternary};
    color: ${props => props.theme.colors.text.primary};
  }

  &::before {
    content: '×';
  }
`;

const EditorWrapper = styled.div`
  flex: 1;
  overflow: hidden;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: ${props => props.theme.colors.text.muted};
  font-size: ${props => props.theme.typography.fontSize.lg};
`;

const EmptyStateTitle = styled.h2`
  margin-bottom: ${props => props.theme.spacing.lg};
  font-weight: ${props => props.theme.typography.fontWeight.normal};
`;

const EmptyStateText = styled.p`
  font-size: ${props => props.theme.typography.fontSize.md};
  text-align: center;
  line-height: ${props => props.theme.typography.lineHeight.relaxed};
`;

interface CodeEditorProps {
  files: FileTab[];
  activeFileId: string | null;
  onFileSelect: (fileId: string) => void;
  onFileClose: (fileId: string) => void;
  onContentChange: (content: string) => void;
}

const CodeEditor: React.FC<CodeEditorProps> = ({
  files,
  activeFileId,
  onFileSelect,
  onFileClose,
  onContentChange
}) => {
  const editorRef = useRef<any>(null);
  const activeFile = files.find(file => file.id === activeFileId);

  useEffect(() => {
    // Configure Monaco Editor theme
    if (editorRef.current) {
      const monaco = editorRef.current;
      monaco.editor.defineTheme('dark-theme', {
        base: 'vs-dark',
        inherit: true,
        rules: [
          { token: 'comment', foreground: '6A9955' },
          { token: 'keyword', foreground: '569CD6' },
          { token: 'string', foreground: 'CE9178' },
          { token: 'number', foreground: 'B5CEA8' },
          { token: 'type', foreground: '4EC9B0' },
          { token: 'class', foreground: '4EC9B0' },
          { token: 'function', foreground: 'DCDCAA' },
          { token: 'variable', foreground: '9CDCFE' },
        ],
        colors: {
          'editor.background': '#1e1e1e',
          'editor.foreground': '#d4d4d4',
          'editor.lineHighlightBackground': '#2a2d2e',
          'editor.selectionBackground': '#264f78',
          'editorCursor.foreground': '#d4d4d4',
          'editorLineNumber.foreground': '#858585',
          'editorGutter.background': '#1e1e1e',
        }
      });
      monaco.editor.setTheme('dark-theme');
    }
  }, []);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = monaco;

    // Configure enhanced editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'Consolas, Monaco, "Courier New", monospace',
      lineHeight: 20,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      wordWrap: 'on',
      automaticLayout: true,
      tabSize: 4,
      insertSpaces: true,
      renderWhitespace: 'selection',
      renderControlCharacters: true,
      folding: true,
      lineNumbers: 'on',
      glyphMargin: true,
      rulers: [80, 120],
      // Enhanced features
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      acceptSuggestionOnCommitCharacter: true,
      snippetSuggestions: 'top',
      wordBasedSuggestions: true,
      parameterHints: { enabled: true },
      quickSuggestions: {
        other: true,
        comments: false,
        strings: false
      },
      // Code formatting
      formatOnPaste: true,
      formatOnType: true,
      // Bracket matching
      matchBrackets: 'always',
      autoClosingBrackets: 'always',
      autoClosingQuotes: 'always',
      autoSurround: 'languageDefined',
      // Selection
      selectOnLineNumbers: true,
      selectionHighlight: true,
      occurrencesHighlight: true,
      // Find and replace
      find: {
        seedSearchStringFromSelection: true,
        autoFindInSelection: 'never'
      }
    });

    // Set up the enhanced theme
    monaco.editor.defineTheme('dark-theme', EDITOR_THEMES['dark-theme']);
    monaco.editor.setTheme('dark-theme');

    // Add custom key bindings
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      // Trigger save file event
      const event = new CustomEvent('save-file');
      window.dispatchEvent(event);
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run();
    });

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.startFindReplaceAction').run();
    });

    // Language-specific configurations
    monaco.languages.typescript.typescriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2020,
      allowNonTsExtensions: true,
      moduleResolution: monaco.languages.typescript.ModuleResolutionKind.NodeJs,
      module: monaco.languages.typescript.ModuleKind.CommonJS,
      noEmit: true,
      esModuleInterop: true,
      jsx: monaco.languages.typescript.JsxEmit.React,
      reactNamespace: 'React',
      allowJs: true,
      typeRoots: ['node_modules/@types']
    });

    // Python-specific snippets
    monaco.languages.registerCompletionItemProvider('python', {
      provideCompletionItems: (model: any, position: any) => {
        const suggestions = [
          {
            label: 'def',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'def ${1:function_name}(${2:parameters}):\n    ${3:pass}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Function definition'
          },
          {
            label: 'class',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'class ${1:ClassName}:\n    def __init__(self${2:, parameters}):\n        ${3:pass}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Class definition'
          },
          {
            label: 'if',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'if ${1:condition}:\n    ${2:pass}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'If statement'
          },
          {
            label: 'for',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'for ${1:item} in ${2:iterable}:\n    ${3:pass}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'For loop'
          }
        ];
        return { suggestions };
      }
    });

    // JavaScript/TypeScript snippets
    monaco.languages.registerCompletionItemProvider('javascript', {
      provideCompletionItems: (model: any, position: any) => {
        const suggestions = [
          {
            label: 'function',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'function ${1:functionName}(${2:parameters}) {\n    ${3:// code}\n}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Function declaration'
          },
          {
            label: 'arrow',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'const ${1:functionName} = (${2:parameters}) => {\n    ${3:// code}\n};',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Arrow function'
          },
          {
            label: 'class',
            kind: monaco.languages.CompletionItemKind.Snippet,
            insertText: 'class ${1:ClassName} {\n    constructor(${2:parameters}) {\n        ${3:// code}\n    }\n}',
            insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
            documentation: 'Class declaration'
          }
        ];
        return { suggestions };
      }
    });
  };

  const handleTabClose = (e: React.MouseEvent, fileId: string) => {
    e.stopPropagation();
    onFileClose(fileId);
  };

  if (files.length === 0) {
    return (
      <EditorContainer>
        <EmptyState>
          <EmptyStateTitle>Welcome to Dark Theme IDE</EmptyStateTitle>
          <EmptyStateText>
            Open a file from the explorer or create a new file to get started.<br />
            Use Ctrl+N to create a new file or Ctrl+O to open an existing file.
          </EmptyStateText>
        </EmptyState>
      </EditorContainer>
    );
  }

  return (
    <EditorContainer>
      <TabBar>
        {files.map(file => (
          <Tab
            key={file.id}
            isActive={file.id === activeFileId}
            isDirty={file.isDirty}
            onClick={() => onFileSelect(file.id)}
          >
            <TabName>{file.name}</TabName>
            <CloseButton onClick={(e) => handleTabClose(e, file.id)} />
          </Tab>
        ))}
      </TabBar>

      <EditorWrapper>
        {activeFile && (
          <Editor
            height="100%"
            language={activeFile.language}
            value={activeFile.content}
            onChange={(value) => onContentChange(value || '')}
            onMount={handleEditorDidMount}
            theme="dark-theme"
            options={{
              fontSize: 14,
              fontFamily: 'Consolas, Monaco, "Courier New", monospace',
              lineHeight: 20,
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              wordWrap: 'on',
              automaticLayout: true,
              tabSize: 4,
              insertSpaces: true,
              renderWhitespace: 'selection',
              renderControlCharacters: true,
              folding: true,
              lineNumbers: 'on',
              glyphMargin: true,
              rulers: [80, 120],
            }}
          />
        )}
      </EditorWrapper>
    </EditorContainer>
  );
};

export default CodeEditor;
