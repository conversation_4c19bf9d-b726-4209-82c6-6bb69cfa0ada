import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const GitContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: ${props => props.theme.colors.secondary};
  border-right: 1px solid ${props => props.theme.colors.border.primary};
`;

const GitHeader = styled.div`
  padding: ${props => props.theme.spacing.md};
  border-bottom: 1px solid ${props => props.theme.colors.border.primary};
  background-color: ${props => props.theme.colors.tertiary};
`;

const GitTitle = styled.h3`
  margin: 0;
  font-size: ${props => props.theme.typography.fontSize.md};
  color: ${props => props.theme.colors.text.primary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
`;

const GitSection = styled.div`
  margin-bottom: ${props => props.theme.spacing.lg};
`;

const SectionTitle = styled.h4`
  margin: 0 0 ${props => props.theme.spacing.sm} 0;
  padding: ${props => props.theme.spacing.sm} ${props => props.theme.spacing.md};
  font-size: ${props => props.theme.typography.fontSize.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.typography.fontWeight.medium};
  background-color: ${props => props.theme.colors.quaternary};
`;

const GitContent = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: ${props => props.theme.spacing.md};
`;

const BranchInfo = styled.div`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.quaternary};
  border-radius: ${props => props.theme.borderRadius.sm};
  margin-bottom: ${props => props.theme.spacing.md};
`;

const BranchIcon = styled.span`
  margin-right: ${props => props.theme.spacing.sm};
  color: ${props => props.theme.colors.accent.primary};
`;

const BranchName = styled.span`
  font-family: ${props => props.theme.typography.fontFamily.mono};
  color: ${props => props.theme.colors.text.primary};
  font-size: ${props => props.theme.typography.fontSize.sm};
`;

const FileList = styled.div`
  display: flex;
  flex-direction: column;
`;

const FileItem = styled.div<{ status: string }>`
  display: flex;
  align-items: center;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  cursor: pointer;
  border-radius: ${props => props.theme.borderRadius.sm};
  font-size: ${props => props.theme.typography.fontSize.sm};

  &:hover {
    background-color: ${props => props.theme.colors.quaternary};
  }
`;

const FileStatus = styled.span<{ status: string }>`
  width: 16px;
  height: 16px;
  margin-right: ${props => props.theme.spacing.sm};
  font-weight: bold;
  text-align: center;
  border-radius: 2px;
  font-size: 10px;
  line-height: 16px;

  ${props => {
    switch (props.status) {
      case 'M':
        return `background-color: #f39c12; color: white;`;
      case 'A':
        return `background-color: #27ae60; color: white;`;
      case 'D':
        return `background-color: #e74c3c; color: white;`;
      case 'R':
        return `background-color: #9b59b6; color: white;`;
      case '??':
        return `background-color: #95a5a6; color: white;`;
      default:
        return `background-color: ${props.theme.colors.quaternary}; color: ${props.theme.colors.text.muted};`;
    }
  }}
`;

const FileName = styled.span`
  color: ${props => props.theme.colors.text.primary};
  font-family: ${props => props.theme.typography.fontFamily.mono};
`;

const ActionButton = styled.button`
  background-color: ${props => props.theme.colors.accent.primary};
  color: white;
  border: none;
  padding: ${props => props.theme.spacing.xs} ${props => props.theme.spacing.sm};
  border-radius: ${props => props.theme.borderRadius.sm};
  cursor: pointer;
  font-size: ${props => props.theme.typography.fontSize.sm};
  margin: ${props => props.theme.spacing.xs} 0;

  &:hover {
    background-color: ${props => props.theme.colors.accent.secondary};
  }

  &:disabled {
    background-color: ${props => props.theme.colors.quaternary};
    color: ${props => props.theme.colors.text.muted};
    cursor: not-allowed;
  }
`;

const CommitInput = styled.textarea`
  width: 100%;
  min-height: 60px;
  padding: ${props => props.theme.spacing.sm};
  background-color: ${props => props.theme.colors.editor.background};
  border: 1px solid ${props => props.theme.colors.border.primary};
  border-radius: ${props => props.theme.borderRadius.sm};
  color: ${props => props.theme.colors.text.primary};
  font-family: ${props => props.theme.typography.fontFamily.primary};
  font-size: ${props => props.theme.typography.fontSize.sm};
  resize: vertical;
  margin-bottom: ${props => props.theme.spacing.sm};

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.accent.primary};
  }

  &::placeholder {
    color: ${props => props.theme.colors.text.muted};
  }
`;

interface GitFile {
  path: string;
  status: string;
}

interface GitStatus {
  branch: string;
  ahead: number;
  behind: number;
  staged: GitFile[];
  unstaged: GitFile[];
  untracked: GitFile[];
}

interface GitPanelProps {
  width: number;
}

const GitPanel: React.FC<GitPanelProps> = ({ width }) => {
  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);
  const [commitMessage, setCommitMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadGitStatus();
  }, []);

  const loadGitStatus = async () => {
    setIsLoading(true);
    try {
      // This would call the Git backend API
      const result = await window.electronAPI?.getGitStatus?.();
      if (result?.success) {
        setGitStatus(result.status);
      }
    } catch (error) {
      console.error('Error loading Git status:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStageFile = async (filePath: string) => {
    try {
      const result = await window.electronAPI?.gitStageFile?.(filePath);
      if (result?.success) {
        loadGitStatus();
      }
    } catch (error) {
      console.error('Error staging file:', error);
    }
  };

  const handleUnstageFile = async (filePath: string) => {
    try {
      const result = await window.electronAPI?.gitUnstageFile?.(filePath);
      if (result?.success) {
        loadGitStatus();
      }
    } catch (error) {
      console.error('Error unstaging file:', error);
    }
  };

  const handleCommit = async () => {
    if (!commitMessage.trim()) return;

    try {
      const result = await window.electronAPI?.gitCommit?.(commitMessage);
      if (result?.success) {
        setCommitMessage('');
        loadGitStatus();
      }
    } catch (error) {
      console.error('Error committing:', error);
    }
  };

  const renderFileList = (files: GitFile[], title: string, onAction?: (path: string) => void, actionText?: string) => (
    <GitSection>
      <SectionTitle>{title} ({files.length})</SectionTitle>
      <FileList>
        {files.map((file, index) => (
          <FileItem key={index} status={file.status} onClick={() => onAction?.(file.path)}>
            <FileStatus status={file.status}>{file.status}</FileStatus>
            <FileName>{file.path}</FileName>
          </FileItem>
        ))}
      </FileList>
    </GitSection>
  );

  if (isLoading) {
    return (
      <GitContainer style={{ width }}>
        <GitHeader>
          <GitTitle>{'>'} git</GitTitle>
        </GitHeader>
        <GitContent>
          <div>Loading Git status...</div>
        </GitContent>
      </GitContainer>
    );
  }

  if (!gitStatus) {
    return (
      <GitContainer style={{ width }}>
        <GitHeader>
          <GitTitle>{'>'} git</GitTitle>
        </GitHeader>
        <GitContent>
          <div>No Git repository found</div>
          <ActionButton onClick={loadGitStatus}>{'>'} refresh</ActionButton>
        </GitContent>
      </GitContainer>
    );
  }

  return (
    <GitContainer style={{ width }}>
      <GitHeader>
        <GitTitle>{'>'} git</GitTitle>
      </GitHeader>
      <GitContent>
        <BranchInfo>
          <BranchIcon>🌿</BranchIcon>
          <BranchName>{gitStatus.branch}</BranchName>
        </BranchInfo>

        {gitStatus.staged.length > 0 && renderFileList(
          gitStatus.staged,
          '> staged changes',
          handleUnstageFile,
          '> unstage'
        )}

        {gitStatus.unstaged.length > 0 && renderFileList(
          gitStatus.unstaged,
          '> changes',
          handleStageFile,
          '> stage'
        )}

        {gitStatus.untracked.length > 0 && renderFileList(
          gitStatus.untracked,
          '> untracked files',
          handleStageFile,
          '> stage'
        )}

        {gitStatus.staged.length > 0 && (
          <GitSection>
            <SectionTitle>{'>'} commit</SectionTitle>
            <CommitInput
              value={commitMessage}
              onChange={(e) => setCommitMessage(e.target.value)}
              placeholder="> enter commit message..."
            />
            <ActionButton
              onClick={handleCommit}
              disabled={!commitMessage.trim()}
            >
              {'>'} commit ({gitStatus.staged.length} files)
            </ActionButton>
          </GitSection>
        )}

        <ActionButton onClick={loadGitStatus}>{'>'} refresh</ActionButton>
      </GitContent>
    </GitContainer>
  );
};

export default GitPanel;
