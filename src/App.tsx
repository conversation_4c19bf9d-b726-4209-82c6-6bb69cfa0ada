import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';
import { darkTheme } from './styles/theme';
import MenuBar from './components/MenuBar/MenuBar';
import Sidebar from './components/Sidebar/FileExplorer';
import CodeEditor from './components/Editor/CodeEditor';
import Terminal from './components/Terminal/Terminal';
import StatusBar from './components/StatusBar/StatusBar';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: ${props => props.theme.typography.fontFamily.primary};
    background-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.text.primary};
    overflow: hidden;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.secondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.quaternary};
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.border.secondary};
  }
`;

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
`;

const MainContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const EditorSection = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
`;

const EditorContainer = styled.div`
  flex: 1;
  overflow: hidden;
`;

const TerminalContainer = styled.div`
  height: 200px;
  border-top: 1px solid ${props => props.theme.colors.border.primary};
`;

export interface FileTab {
  id: string;
  name: string;
  path: string;
  content: string;
  isDirty: boolean;
  language: string;
}

const App: React.FC = () => {
  const [openFiles, setOpenFiles] = useState<FileTab[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [sidebarWidth] = useState(250);
  const [terminalHeight] = useState(200);
  const [isTerminalVisible, setIsTerminalVisible] = useState(false);

  const activeFile = openFiles.find(file => file.id === activeFileId);

  const getLanguageFromExtension = (extension: string): string => {
    const ext = extension.toLowerCase().replace('.', '');

    const languageMap: { [key: string]: string } = {
      // Python
      'py': 'python',
      'pyw': 'python',
      'pyi': 'python',

      // JavaScript/TypeScript
      'js': 'javascript',
      'jsx': 'javascript',
      'mjs': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',

      // Web Technologies
      'html': 'html',
      'htm': 'html',
      'xhtml': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'scss',
      'less': 'less',

      // Data formats
      'json': 'json',
      'xml': 'xml',
      'yml': 'yaml',
      'yaml': 'yaml',

      // Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'txt': 'plaintext',
      'text': 'plaintext',

      // Shell scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',

      // Other languages
      'sql': 'sql',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'c',
      'hpp': 'cpp'
    };

    return languageMap[ext] || 'plaintext';
  };

  const handleFileSelect = async (filePath: string) => {
    try {
      console.log('Attempting to open file:', filePath);
      const result = await window.electronAPI.readFile(filePath);
      console.log('File read result:', result);
      if (result.success) {
        const fileName = window.electronAPI?.basename(filePath) || 'Unknown';
        const extension = window.electronAPI?.extname(filePath) || '';
        const language = getLanguageFromExtension(extension);

        console.log('File details:', { fileName, extension, language });

        const existingFile = openFiles.find(file => file.path === filePath);
        if (existingFile) {
          console.log('File already open, switching to it:', existingFile.id);
          setActiveFileId(existingFile.id);
        } else {
          const newFile: FileTab = {
            id: filePath,
            name: fileName,
            path: filePath,
            content: result.content || '',
            isDirty: false,
            language
          };
          console.log('Creating new file tab:', newFile);
          setOpenFiles(prev => {
            const newFiles = [...prev, newFile];
            console.log('Updated open files:', newFiles);
            return newFiles;
          });
          setActiveFileId(newFile.id);
          console.log('Set active file ID:', newFile.id);
        }
      } else {
        console.error('Failed to read file:', result.error);
      }
    } catch (error) {
      console.error('Error opening file:', error);
    }
  };

  const handleFileContentChange = (content: string) => {
    if (activeFile) {
      setOpenFiles(prev => prev.map(file =>
        file.id === activeFile.id
          ? { ...file, content, isDirty: content !== file.content }
          : file
      ));
    }
  };

  const handleCloseFile = (fileId: string) => {
    setOpenFiles(prev => {
      const newFiles = prev.filter(file => file.id !== fileId);
      if (activeFileId === fileId) {
        setActiveFileId(newFiles.length > 0 ? newFiles[newFiles.length - 1].id : null);
      }
      return newFiles;
    });
  };

  return (
    <ThemeProvider theme={darkTheme}>
      <GlobalStyle />
      <AppContainer>
        <MenuBar />
        <MainContent>
          <Sidebar
            width={sidebarWidth}
            onFileSelect={handleFileSelect}
          />
          <EditorSection>
            <EditorContainer>
              <CodeEditor
                files={openFiles}
                activeFileId={activeFileId}
                onFileSelect={setActiveFileId}
                onFileClose={handleCloseFile}
                onContentChange={handleFileContentChange}
              />
            </EditorContainer>
            {isTerminalVisible && (
              <TerminalContainer>
                <Terminal height={terminalHeight} />
              </TerminalContainer>
            )}
          </EditorSection>
        </MainContent>
        <StatusBar
          activeFile={activeFile}
          onToggleTerminal={() => setIsTerminalVisible(!isTerminalVisible)}
          isTerminalVisible={isTerminalVisible}
        />
      </AppContainer>
    </ThemeProvider>
  );
};

export default App;
