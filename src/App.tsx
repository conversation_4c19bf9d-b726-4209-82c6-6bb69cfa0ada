import React, { useState, useEffect } from 'react';
import styled, { ThemeProvider, createGlobalStyle } from 'styled-components';
import { darkTheme } from './styles/theme';
import MenuBar from './components/MenuBar/MenuBar';
import Sidebar from './components/Sidebar/FileExplorer';
import CodeEditor from './components/Editor/CodeEditor';
import Terminal from './components/Terminal/Terminal';
import StatusBar from './components/StatusBar/StatusBar';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: ${props => props.theme.typography.fontFamily.primary};
    background-color: ${props => props.theme.colors.primary};
    color: ${props => props.theme.colors.text.primary};
    overflow: hidden;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: ${props => props.theme.colors.secondary};
  }

  ::-webkit-scrollbar-thumb {
    background: ${props => props.theme.colors.quaternary};
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: ${props => props.theme.colors.border.secondary};
  }
`;

const AppContainer = styled.div`
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
`;

const MainContent = styled.div`
  display: flex;
  flex: 1;
  overflow: hidden;
`;

const EditorSection = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
`;

const EditorContainer = styled.div`
  flex: 1;
  overflow: hidden;
`;

const TerminalContainer = styled.div`
  height: 200px;
  border-top: 1px solid ${props => props.theme.colors.border.primary};
`;

export interface FileTab {
  id: string;
  name: string;
  path: string;
  content: string;
  isDirty: boolean;
  language: string;
}

const App: React.FC = () => {
  const [openFiles, setOpenFiles] = useState<FileTab[]>([]);
  const [activeFileId, setActiveFileId] = useState<string | null>(null);
  const [sidebarWidth] = useState(250);
  const [terminalHeight] = useState(200);
  const [isTerminalVisible, setIsTerminalVisible] = useState(false);

  const activeFile = openFiles.find(file => file.id === activeFileId);

  // Test IPC communication on component mount
  useEffect(() => {
    const testIPC = async () => {
      try {
        console.log('=== IPC TEST START ===');
        console.log('electronAPI available:', !!window.electronAPI);
        console.log('Available methods:', Object.keys(window.electronAPI || {}));

        if (window.electronAPI?.getCwd) {
          const cwd = await window.electronAPI.getCwd();
          console.log('Current working directory:', cwd);
        } else {
          console.error('getCwd not available');
        }
        console.log('=== IPC TEST END ===');
      } catch (error) {
        console.error('IPC test failed:', error);
      }
    };

    testIPC();
  }, []);

  // Handle menu events
  useEffect(() => {
    const handleNewFile = () => {
      const newFile: FileTab = {
        id: `untitled-${Date.now()}`,
        name: 'Untitled',
        path: '',
        content: '',
        isDirty: false,
        language: 'plaintext'
      };
      setOpenFiles(prev => [...prev, newFile]);
      setActiveFileId(newFile.id);
    };

    const handleOpenFile = async (event: any, filePath: string) => {
      try {
        const result = await window.electronAPI.readFile(filePath);
        if (result.success) {
          const fileName = window.electronAPI?.basename(filePath) || 'Unknown';
          const extension = window.electronAPI?.extname(filePath) || '';
          const language = getLanguageFromExtension(extension);

          const newFile: FileTab = {
            id: filePath,
            name: fileName,
            path: filePath,
            content: result.content || '',
            isDirty: false,
            language
          };

          // Check if file is already open
          const existingFile = openFiles.find(file => file.path === filePath);
          if (existingFile) {
            setActiveFileId(existingFile.id);
          } else {
            setOpenFiles(prev => [...prev, newFile]);
            setActiveFileId(newFile.id);
          }
        }
      } catch (error) {
        console.error('Error opening file:', error);
      }
    };

    const handleSaveFile = async () => {
      if (activeFile) {
        try {
          if (activeFile.path) {
            await window.electronAPI.writeFile(activeFile.path, activeFile.content);
            setOpenFiles(prev => prev.map(file =>
              file.id === activeFile.id ? { ...file, isDirty: false } : file
            ));
          } else {
            // Save as new file
            const result = await window.electronAPI.saveFileDialog();
            if (!result.canceled && result.filePath) {
              await window.electronAPI.writeFile(result.filePath, activeFile.content);
              const fileName = window.electronAPI?.basename(result.filePath!) || 'Unknown';
              setOpenFiles(prev => prev.map(file =>
                file.id === activeFile.id
                  ? { ...file, name: fileName, path: result.filePath!, isDirty: false }
                  : file
              ));
            }
          }
        } catch (error) {
          console.error('Error saving file:', error);
        }
      }
    };

    window.electronAPI.onMenuNewFile(handleNewFile);
    window.electronAPI.onMenuOpenFile(handleOpenFile);
    window.electronAPI.onMenuSaveFile(handleSaveFile);

    return () => {
      window.electronAPI.removeAllListeners('menu-new-file');
      window.electronAPI.removeAllListeners('menu-open-file');
      window.electronAPI.removeAllListeners('menu-save-file');
    };
  }, [openFiles, activeFile]);

  const getLanguageFromExtension = (extension: string): string => {
    const ext = extension.toLowerCase().replace('.', '');

    const languageMap: { [key: string]: string } = {
      // Python
      'py': 'python',
      'pyw': 'python',
      'pyi': 'python',

      // JavaScript/TypeScript
      'js': 'javascript',
      'jsx': 'javascript',
      'mjs': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',

      // Web Technologies
      'html': 'html',
      'htm': 'html',
      'xhtml': 'html',
      'css': 'css',
      'scss': 'scss',
      'sass': 'scss',
      'less': 'less',

      // Data formats
      'json': 'json',
      'xml': 'xml',
      'yml': 'yaml',
      'yaml': 'yaml',

      // Documentation
      'md': 'markdown',
      'markdown': 'markdown',
      'txt': 'plaintext',
      'text': 'plaintext',

      // Shell scripts
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'fish': 'shell',

      // Other languages
      'sql': 'sql',
      'php': 'php',
      'rb': 'ruby',
      'go': 'go',
      'rs': 'rust',
      'java': 'java',
      'c': 'c',
      'cpp': 'cpp',
      'h': 'c',
      'hpp': 'cpp'
    };

    return languageMap[ext] || 'plaintext';
  };

  const handleFileSelect = async (filePath: string) => {
    try {
      console.log('Attempting to open file:', filePath);
      const result = await window.electronAPI.readFile(filePath);
      console.log('File read result:', result);
      if (result.success) {
        const fileName = window.electronAPI?.basename(filePath) || 'Unknown';
        const extension = window.electronAPI?.extname(filePath) || '';
        const language = getLanguageFromExtension(extension);

        const existingFile = openFiles.find(file => file.path === filePath);
        if (existingFile) {
          setActiveFileId(existingFile.id);
        } else {
          const newFile: FileTab = {
            id: filePath,
            name: fileName,
            path: filePath,
            content: result.content || '',
            isDirty: false,
            language
          };
          setOpenFiles(prev => [...prev, newFile]);
          setActiveFileId(newFile.id);
        }
      }
    } catch (error) {
      console.error('Error opening file:', error);
    }
  };

  const handleFileContentChange = (content: string) => {
    if (activeFile) {
      setOpenFiles(prev => prev.map(file =>
        file.id === activeFile.id
          ? { ...file, content, isDirty: content !== file.content }
          : file
      ));
    }
  };

  const handleCloseFile = (fileId: string) => {
    setOpenFiles(prev => {
      const newFiles = prev.filter(file => file.id !== fileId);
      if (activeFileId === fileId) {
        setActiveFileId(newFiles.length > 0 ? newFiles[newFiles.length - 1].id : null);
      }
      return newFiles;
    });
  };

  return (
    <ThemeProvider theme={darkTheme}>
      <GlobalStyle />
      <AppContainer>
        <MenuBar />
        <MainContent>
          <Sidebar
            width={sidebarWidth}
            onFileSelect={handleFileSelect}
          />
          <EditorSection>
            <EditorContainer>
              <CodeEditor
                files={openFiles}
                activeFileId={activeFileId}
                onFileSelect={setActiveFileId}
                onFileClose={handleCloseFile}
                onContentChange={handleFileContentChange}
              />
            </EditorContainer>
            {isTerminalVisible && (
              <TerminalContainer>
                <Terminal height={terminalHeight} />
              </TerminalContainer>
            )}
          </EditorSection>
        </MainContent>
        <StatusBar
          activeFile={activeFile}
          onToggleTerminal={() => setIsTerminalVisible(!isTerminalVisible)}
          isTerminalVisible={isTerminalVisible}
        />
      </AppContainer>
    </ThemeProvider>
  );
};

export default App;
