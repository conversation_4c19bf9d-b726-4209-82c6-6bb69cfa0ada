const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

console.log('=== PRELOAD SCRIPT LOADED ===');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  saveFileDialog: () => ipcRenderer.invoke('save-file-dialog'),
  readDirectory: (dirPath) => ipcRenderer.invoke('read-directory', dirPath),

  // Menu events
  onMenuNewFile: (callback) => ipcRenderer.on('menu-new-file', callback),
  onMenuOpenFile: (callback) => ipcRenderer.on('menu-open-file', callback),
  onMenuSaveFile: (callback) => ipcRenderer.on('menu-save-file', callback),

  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),

  // Python backend communication
  executePython: (code) => {
    return fetch('http://localhost:8002/execute', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ code })
    }).then(response => response.json());
  },

  // Get current working directory
  getCwd: () => ipcRenderer.invoke('get-cwd'),

  // Path utilities
  joinPath: (...paths) => require('path').join(...paths),
  dirname: (path) => require('path').dirname(path),
  basename: (path) => require('path').basename(path),
  extname: (path) => require('path').extname(path)
});
