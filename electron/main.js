const { app, BrowserWindow, ipcMain, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs').promises;
const { spawn, exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

let mainWindow;
let pythonProcess;

function createWindow() {
  console.log('Creating Electron window...');

  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 800,
    minHeight: 600,
    title: '>CØDE',
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false // Allow loading from localhost
    },
    titleBarStyle: 'default',
    show: false, // Don't show until ready
    backgroundColor: '#1e1e1e',
    center: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    alwaysOnTop: false,
    skipTaskbar: false
  });

  console.log('Window created, loading content...');

  // Load the app
  const isDev = process.env.NODE_ENV === 'development';
  if (isDev) {
    console.log('Loading development URL: http://localhost:3000');
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    console.log('Loading production build');
    mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    console.log('Window ready to show');
    mainWindow.show();
    mainWindow.focus();
  });

  mainWindow.webContents.on('did-finish-load', () => {
    console.log('Content loaded successfully');
  });

  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load content:', errorCode, errorDescription);
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Create menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: '>file',
      submenu: [
        {
          label: '>new',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-new-file');
          }
        },
        {
          label: '>open',
          accelerator: 'CmdOrCtrl+O',
          click: async () => {
            const result = await dialog.showOpenDialog(mainWindow, {
              properties: ['openFile'],
              filters: [
                { name: 'All Files', extensions: ['*'] },
                { name: 'Python', extensions: ['py'] },
                { name: 'JavaScript', extensions: ['js', 'jsx'] },
                { name: 'TypeScript', extensions: ['ts', 'tsx'] },
                { name: 'HTML', extensions: ['html'] },
                { name: 'CSS', extensions: ['css'] },
                { name: 'JSON', extensions: ['json'] },
                { name: 'Text', extensions: ['txt'] }
              ]
            });

            if (!result.canceled) {
              mainWindow.webContents.send('menu-open-file', result.filePaths[0]);
            }
          }
        },
        {
          label: '>save',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            mainWindow.webContents.send('menu-save-file');
          }
        },
        { type: 'separator' },
        {
          label: '>exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: '>edit',
      submenu: [
        { label: '>undo', role: 'undo' },
        { label: '>redo', role: 'redo' },
        { type: 'separator' },
        { label: '>cut', role: 'cut' },
        { label: '>copy', role: 'copy' },
        { label: '>paste', role: 'paste' },
        { type: 'separator' },
        { label: '>find', accelerator: 'CmdOrCtrl+F' },
        { label: '>replace', accelerator: 'CmdOrCtrl+H' }
      ]
    },
    {
      label: '>view',
      submenu: [
        { label: '>reload', role: 'reload' },
        { label: '>force reload', role: 'forceReload' },
        { label: '>dev tools', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: '>reset zoom', role: 'resetZoom' },
        { label: '>zoom in', role: 'zoomIn' },
        { label: '>zoom out', role: 'zoomOut' },
        { type: 'separator' },
        { label: '>fullscreen', role: 'togglefullscreen' }
      ]
    },
    {
      label: '>git',
      submenu: [
        { label: '>status' },
        { label: '>commit' },
        { label: '>push' },
        { label: '>pull' },
        { type: 'separator' },
        { label: '>branches' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// IPC handlers
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return { success: true, content };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('write-file', async (event, filePath, content) => {
  try {
    await fs.writeFile(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('save-file-dialog', async (event) => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'All Files', extensions: ['*'] },
      { name: 'Python', extensions: ['py'] },
      { name: 'JavaScript', extensions: ['js'] },
      { name: 'TypeScript', extensions: ['ts'] },
      { name: 'Text', extensions: ['txt'] }
    ]
  });

  return result;
});

ipcMain.handle('read-directory', async (event, dirPath) => {
  try {
    const items = await fs.readdir(dirPath, { withFileTypes: true });
    const result = items.map(item => ({
      name: item.name,
      isDirectory: item.isDirectory(),
      path: path.join(dirPath, item.name)
    }));
    return { success: true, items: result };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Git operations
ipcMain.handle('git-status', async (event) => {
  try {
    const cwd = process.cwd();
    const { stdout } = await execAsync('git status --porcelain -b', { cwd });

    const lines = stdout.trim().split('\n').filter(line => line);
    const branchLine = lines.find(line => line.startsWith('##'));
    const fileLines = lines.filter(line => !line.startsWith('##'));

    // Parse branch info
    let branch = 'main';
    let ahead = 0;
    let behind = 0;

    if (branchLine) {
      const branchMatch = branchLine.match(/## (.+?)(?:\.\.\.|$)/);
      if (branchMatch) {
        branch = branchMatch[1];
      }

      const aheadMatch = branchLine.match(/ahead (\d+)/);
      const behindMatch = branchLine.match(/behind (\d+)/);

      if (aheadMatch) ahead = parseInt(aheadMatch[1]);
      if (behindMatch) behind = parseInt(behindMatch[1]);
    }

    // Parse file changes
    const staged = [];
    const unstaged = [];
    const untracked = [];

    fileLines.forEach(line => {
      const status = line.substring(0, 2);
      const filePath = line.substring(3);

      if (status === '??') {
        untracked.push({ path: filePath, status: '??' });
      } else {
        if (status[0] !== ' ') {
          staged.push({ path: filePath, status: status[0] });
        }
        if (status[1] !== ' ') {
          unstaged.push({ path: filePath, status: status[1] });
        }
      }
    });

    return {
      success: true,
      status: {
        branch,
        ahead,
        behind,
        staged,
        unstaged,
        untracked
      }
    };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('git-stage-file', async (event, filePath) => {
  try {
    const cwd = process.cwd();
    await execAsync(`git add "${filePath}"`, { cwd });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('git-unstage-file', async (event, filePath) => {
  try {
    const cwd = process.cwd();
    await execAsync(`git reset HEAD "${filePath}"`, { cwd });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('git-commit', async (event, message) => {
  try {
    const cwd = process.cwd();
    await execAsync(`git commit -m "${message.replace(/"/g, '\\"')}"`, { cwd });
    return { success: true };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

// Start Python backend
function startPythonBackend() {
  try {
    const pythonScript = path.join(__dirname, '../python_backend/server.py');
    console.log('Attempting to start Python backend...');

    // Try python3 first, then python
    pythonProcess = spawn('python3', [pythonScript]);

    pythonProcess.stdout.on('data', (data) => {
      console.log(`Python backend: ${data}`);
    });

    pythonProcess.stderr.on('data', (data) => {
      console.error(`Python backend error: ${data}`);
    });

    pythonProcess.on('error', (error) => {
      console.error('Failed to start Python backend:', error.message);
      console.log('Python backend may already be running or not available');
      pythonProcess = null;
    });

    pythonProcess.on('exit', (code) => {
      console.log(`Python backend exited with code ${code}`);
      pythonProcess = null;
    });

  } catch (error) {
    console.error('Error starting Python backend:', error.message);
    pythonProcess = null;
  }
}

app.whenReady().then(() => {
  createWindow();
  startPythonBackend();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (pythonProcess && !pythonProcess.killed) {
    console.log('Killing Python process');
    try {
      pythonProcess.kill('SIGTERM');
    } catch (error) {
      console.error('Error killing Python process:', error.message);
    }
  }
  app.quit();
});

app.on('before-quit', () => {
  console.log('App quitting');
  if (pythonProcess && !pythonProcess.killed) {
    try {
      pythonProcess.kill('SIGTERM');
    } catch (error) {
      console.error('Error killing Python process:', error.message);
    }
  }
});

app.on('will-quit', (event) => {
  console.log('App will quit');
  if (pythonProcess && !pythonProcess.killed) {
    try {
      pythonProcess.kill('SIGTERM');
    } catch (error) {
      console.error('Error killing Python process:', error.message);
    }
  }
});
