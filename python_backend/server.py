#!/usr/bin/env python3
"""
Python Backend Server for Dark Theme IDE
Provides code execution and file operations via HTTP API
"""

import sys
import io
import traceback
import json
import threading
import time
from contextlib import redirect_stdout, redirect_stderr
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import os

class IDERequestHandler(BaseHTTPRequestHandler):
    """HTTP request handler for IDE backend operations"""
    
    def do_OPTIONS(self):
        """Handle CORS preflight requests"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Handle POST requests"""
        try:
            # Parse URL
            parsed_path = urlparse(self.path)
            
            # Set CORS headers
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if parsed_path.path == '/execute':
                self.handle_execute()
            elif parsed_path.path == '/health':
                self.handle_health()
            else:
                self.send_error_response('Unknown endpoint', 404)
                
        except Exception as e:
            self.send_error_response(f'Server error: {str(e)}', 500)
    
    def do_GET(self):
        """Handle GET requests"""
        try:
            parsed_path = urlparse(self.path)
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            if parsed_path.path == '/health':
                self.handle_health()
            elif parsed_path.path == '/status':
                self.handle_status()
            else:
                self.send_error_response('Unknown endpoint', 404)
                
        except Exception as e:
            self.send_error_response(f'Server error: {str(e)}', 500)
    
    def handle_execute(self):
        """Execute Python code and return results"""
        try:
            # Read request body
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            code = data.get('code', '')
            if not code.strip():
                self.send_json_response({
                    'success': False,
                    'error': 'No code provided'
                })
                return
            
            # Execute code safely
            result = self.execute_python_code(code)
            self.send_json_response(result)
            
        except json.JSONDecodeError:
            self.send_error_response('Invalid JSON', 400)
        except Exception as e:
            self.send_error_response(f'Execution error: {str(e)}', 500)
    
    def handle_health(self):
        """Health check endpoint"""
        self.send_json_response({
            'status': 'healthy',
            'service': 'Dark Theme IDE Python Backend',
            'version': '1.0.0',
            'timestamp': time.time()
        })
    
    def handle_status(self):
        """Status endpoint with system information"""
        self.send_json_response({
            'status': 'running',
            'python_version': sys.version,
            'working_directory': os.getcwd(),
            'platform': sys.platform,
            'timestamp': time.time()
        })
    
    def execute_python_code(self, code):
        """
        Execute Python code in a controlled environment
        Returns dict with success, output, and error information
        """
        # Capture stdout and stderr
        stdout_capture = io.StringIO()
        stderr_capture = io.StringIO()
        
        # Create a restricted globals environment
        safe_globals = {
            '__builtins__': {
                'print': print,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'set': set,
                'range': range,
                'enumerate': enumerate,
                'zip': zip,
                'map': map,
                'filter': filter,
                'sorted': sorted,
                'sum': sum,
                'min': min,
                'max': max,
                'abs': abs,
                'round': round,
                'type': type,
                'isinstance': isinstance,
                'hasattr': hasattr,
                'getattr': getattr,
                'setattr': setattr,
                'dir': dir,
                'help': help,
                # Math functions
                'pow': pow,
                # String functions
                'ord': ord,
                'chr': chr,
                # Import statement (limited)
                '__import__': __import__,
            },
            # Common modules that are generally safe
            'math': __import__('math'),
            'random': __import__('random'),
            'datetime': __import__('datetime'),
            'json': __import__('json'),
            're': __import__('re'),
        }
        
        try:
            # Redirect stdout and stderr
            with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                # Execute the code
                exec(code, safe_globals, {})
            
            # Get captured output
            output = stdout_capture.getvalue()
            error_output = stderr_capture.getvalue()
            
            return {
                'success': True,
                'output': output if output else None,
                'error': error_output if error_output else None
            }
            
        except Exception as e:
            # Capture the full traceback
            error_traceback = traceback.format_exc()
            
            return {
                'success': False,
                'output': stdout_capture.getvalue() if stdout_capture.getvalue() else None,
                'error': error_traceback
            }
    
    def send_json_response(self, data):
        """Send JSON response"""
        response = json.dumps(data, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def send_error_response(self, message, status_code=400):
        """Send error response"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        error_response = {
            'success': False,
            'error': message,
            'status_code': status_code
        }
        
        response = json.dumps(error_response, indent=2)
        self.wfile.write(response.encode('utf-8'))
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def start_server(host='localhost', port=8000):
    """Start the HTTP server"""
    server_address = (host, port)
    httpd = HTTPServer(server_address, IDERequestHandler)
    
    print(f"Dark Theme IDE Python Backend starting...")
    print(f"Server running on http://{host}:{port}")
    print(f"Endpoints:")
    print(f"  POST /execute - Execute Python code")
    print(f"  GET  /health  - Health check")
    print(f"  GET  /status  - Server status")
    print(f"Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\nShutting down server...")
        httpd.shutdown()
        httpd.server_close()

if __name__ == '__main__':
    # Default configuration
    HOST = 'localhost'
    PORT = 8000
    
    # Allow command line arguments for host and port
    if len(sys.argv) > 1:
        PORT = int(sys.argv[1])
    if len(sys.argv) > 2:
        HOST = sys.argv[2]
    
    start_server(HOST, PORT)
