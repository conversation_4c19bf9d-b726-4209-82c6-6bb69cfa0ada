# Dark Theme IDE

A modern, custom dark theme IDE built with React, Electron, and Python. Features a sleek dark interface, integrated code editor with syntax highlighting, file explorer, terminal, and Python code execution capabilities.

## Features

### 🎨 **Dark Theme Interface**
- Custom dark theme optimized for long coding sessions
- Consistent color scheme across all components
- Modern, clean UI design

### 📝 **Code Editor**
- Monaco Editor integration with syntax highlighting
- Support for multiple programming languages
- Tab-based file management
- Auto-save indicators
- Line numbers and code folding

### 📁 **File Explorer**
- Tree-view file navigation
- Support for opening files and directories
- File type icons and indicators

### 💻 **Integrated Terminal**
- Built-in terminal with command execution
- Python REPL for interactive Python development
- Command history and auto-completion

### 🐍 **Python Integration**
- Execute Python code directly in the IDE
- Safe code execution environment
- Real-time output and error reporting

### ⚡ **Modern Architecture**
- Electron for cross-platform desktop application
- React with TypeScript for the frontend
- Python backend for code execution
- Styled-components for theming

## Prerequisites

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **Python** (v3.8 or higher)

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd dark-theme-ide
   ```

2. **Install Node.js dependencies:**
   ```bash
   npm install
   ```

3. **Install Python dependencies (optional):**
   ```bash
   pip install -r requirements.txt
   ```

## Development

### Running in Development Mode

Start all services with a single command:
```bash
npm start
```

This will:
- Start the Python backend server on `http://localhost:8000`
- Start the React development server on `http://localhost:3000`
- Launch the Electron application

### Running Individual Services

**Start React development server:**
```bash
npm run start:react
```

**Start Python backend:**
```bash
npm run start:python
```

**Start Electron (after React is running):**
```bash
npm run start:electron
```

## Building for Production

### Build the React application:
```bash
npm run build
```

### Build the Electron application:
```bash
npm run build:electron
```

### Build and package for distribution:
```bash
npm run dist
```

## Project Structure

```
dark-theme-ide/
├── src/                          # React frontend source
│   ├── components/               # React components
│   │   ├── Editor/              # Code editor component
│   │   ├── Sidebar/             # File explorer component
│   │   ├── Terminal/            # Terminal component
│   │   ├── MenuBar/             # Menu bar component
│   │   └── StatusBar/           # Status bar component
│   ├── hooks/                   # Custom React hooks
│   ├── styles/                  # Theme and styling
│   ├── App.tsx                  # Main React application
│   └── index.tsx                # React entry point
├── electron/                    # Electron main process
│   ├── main.js                  # Electron main process
│   └── preload.js               # Electron preload script
├── python_backend/              # Python backend
│   └── server.py                # Python HTTP server
├── public/                      # Static assets
├── build/                       # Built React application
├── dist/                        # Packaged Electron application
├── package.json                 # Node.js dependencies and scripts
├── tsconfig.json                # TypeScript configuration
├── webpack.config.js            # Webpack configuration
└── requirements.txt             # Python dependencies
```

## Usage

### Opening Files
- Use `Ctrl+O` (or `Cmd+O` on Mac) to open files
- Click on files in the file explorer
- Drag and drop files into the editor

### Creating New Files
- Use `Ctrl+N` (or `Cmd+N` on Mac) to create new files
- Use the File menu

### Saving Files
- Use `Ctrl+S` (or `Cmd+S` on Mac) to save files
- Files with unsaved changes show a dot indicator

### Using the Terminal
- Switch between Terminal and Python tabs
- Execute system commands in Terminal tab
- Execute Python code in Python tab

### Python Code Execution
- Switch to the Python tab in the terminal
- Type Python code and press Enter
- View output and errors in real-time

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+N` / `Cmd+N` | New file |
| `Ctrl+O` / `Cmd+O` | Open file |
| `Ctrl+S` / `Cmd+S` | Save file |
| `Ctrl+Q` / `Cmd+Q` | Quit application |
| `F12` | Toggle Developer Tools |

## Customization

### Modifying the Theme
Edit `src/styles/theme.ts` to customize colors, fonts, and spacing.

### Adding Language Support
The Monaco Editor supports many languages out of the box. Language detection is based on file extensions in `src/App.tsx`.

### Extending Python Backend
Modify `python_backend/server.py` to add new endpoints or functionality.

## Security Considerations

The Python backend runs code in a restricted environment, but it's not completely sandboxed. For production use, consider:
- Running the Python backend in a container
- Implementing additional security measures
- Limiting available Python modules and functions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Troubleshooting

### Common Issues

**Python backend not starting:**
- Ensure Python is installed and accessible via `python` command
- Check if port 8000 is available
- Verify Python dependencies are installed

**Electron not launching:**
- Ensure React development server is running first
- Check for any console errors
- Try clearing node_modules and reinstalling

**File operations not working:**
- Check file permissions
- Ensure the application has access to the file system
- Verify file paths are correct

### Getting Help

If you encounter issues:
1. Check the console for error messages
2. Verify all dependencies are installed
3. Ensure all services are running
4. Check the GitHub issues page for known problems

## Roadmap

Future enhancements planned:
- [ ] Git integration
- [ ] Plugin system
- [ ] Multiple theme support
- [ ] Advanced debugging features
- [ ] Language server protocol support
- [ ] Project management features
- [ ] Search and replace functionality
- [ ] Code formatting and linting integration
